import { ref } from 'vue';

import { defineStore } from 'pinia';

// 公司信息接口
export interface CompanyInfo {
  id: string;
  name: string;
}

// 用户信息缓存接口
export interface UserCustomerData {
  username: string;
  tenant_id: string;
  data_source: string;
  saas_id: string;
  company_id: string;
  account_id: string;
  customer_names: string[];
  cached_at: number; // 缓存时间戳
}

export const useCompanySelectionStore = defineStore(
  'company-selection',
  () => {
    // 存储选中的公司名称
    const selectedCompany = ref<string>(''); // 默认为空，等待从API获取后设置

    // 存储公司列表缓存
    const companyListCache = ref<CompanyInfo[]>([]);

    // 存储用户客户数据缓存
    const userCustomerDataCache = ref<UserCustomerData | null>(null);

    // 缓存有效期（30分钟）
    const CACHE_DURATION = 30 * 60 * 1000;

    // 设置选中的公司
    const setSelectedCompany = (companyName: string) => {
      selectedCompany.value = companyName;
    };

    // 获取选中的公司
    const getSelectedCompany = () => {
      return selectedCompany.value;
    };

    // 设置公司列表缓存
    const setCompanyListCache = (companies: CompanyInfo[]) => {
      companyListCache.value = companies;
    };

    // 获取公司列表缓存
    const getCompanyListCache = () => {
      return companyListCache.value;
    };

    // 设置用户客户数据缓存
    const setUserCustomerDataCache = (data: UserCustomerData) => {
      userCustomerDataCache.value = data;
    };

    // 获取用户客户数据缓存
    const getUserCustomerDataCache = () => {
      return userCustomerDataCache.value;
    };

    // 检查缓存是否有效
    const isCacheValid = (username: string, tenant_id: string): boolean => {
      if (!userCustomerDataCache.value) {
        return false;
      }

      const cache = userCustomerDataCache.value;

      // 检查用户信息是否匹配
      if (cache.username !== username || cache.tenant_id !== tenant_id) {
        return false;
      }

      // 检查缓存是否过期
      const now = Date.now();
      const isExpired = (now - cache.cached_at) > CACHE_DURATION;

      return !isExpired;
    };

    // 清除缓存
    const clearCache = () => {
      companyListCache.value = [];
      userCustomerDataCache.value = null;
    };

    // 清除过期缓存
    const clearExpiredCache = () => {
      if (userCustomerDataCache.value) {
        const now = Date.now();
        const isExpired = (now - userCustomerDataCache.value.cached_at) > CACHE_DURATION;
        if (isExpired) {
          clearCache();
        }
      }
    };

    return {
      selectedCompany,
      companyListCache,
      userCustomerDataCache,
      setSelectedCompany,
      getSelectedCompany,
      setCompanyListCache,
      getCompanyListCache,
      setUserCustomerDataCache,
      getUserCustomerDataCache,
      isCacheValid,
      clearCache,
      clearExpiredCache,
    };
  },
  {
    persist: true, // 持久化存储
  },
);

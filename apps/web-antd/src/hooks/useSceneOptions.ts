import type { ComputedRef } from 'vue';
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { fetchScenarioEntryList } from '#/api/original-voucher/api-v2';

export interface SceneOption {
  label: string;
  value: string;
}

export interface CompanyNameRef {
  value: string | ComputedRef<string>;
}

export function useSceneOptions(companyNameRef: CompanyNameRef) {
  const sceneOptions = ref<SceneOption[]>([]);
  const loading = ref(false);

  // 获取场景选项
  async function fetchSceneOptions() {
    const companyName = typeof companyNameRef.value === 'string'
      ? companyNameRef.value
      : companyNameRef.value.value;

    if (!companyName) {
      sceneOptions.value = [];
      return;
    }

    try {
      loading.value = true;
      const params = {
        company_name: companyName,
        needDefault: 1 as 1,
        status: 1 as 1,
      };
      const result = await fetchScenarioEntryList(params);
      const scenes = result?.data || [];
      // 提取唯一的场景名称
      const uniqueScenes = [
        ...new Set(scenes.map((item) => item.scene)),
      ].filter(Boolean);
      sceneOptions.value = uniqueScenes.map((scene) => ({
        label: scene,
        value: scene,
      }));
    } catch (error) {
      console.error('获取场景选项失败:', error);
      message.error('获取场景选项失败');
      sceneOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 监听公司名称变化
  watch(
    () => typeof companyNameRef.value === 'string'
      ? companyNameRef.value
      : companyNameRef.value.value,
    () => {
      fetchSceneOptions();
    },
    { immediate: true }, // 立即执行一次
  );

  return {
    sceneOptions,
    loading,
    fetchSceneOptions,
  };
} 

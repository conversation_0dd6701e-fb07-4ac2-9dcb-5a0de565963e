import type { BaseEntity, PageQuery } from '#/api/common';

export interface FixedAssetVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 累计减值金额
   */
  accumulatedDecrement: number;

  /**
   * 累计折旧科目名称
   */
  accumulatedDepreciationSubject: string;

  /**
   * 累计折旧金额
   */
  accumulatedDepreciation: number;

  /**
   * 是否生成凭证(0-否 1-是)
   */
  isAddVoucher: string;

  /**
   * 资产编码
   */
  assetCode: string;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 资产类型
   */
  assetType: string;

  /**
   * 开始折旧金额
   */
  beginDepreciation: number;

  /**
   * 原始开始折旧金额
   */
  originalBeginDepreciation: number;

  /**
   * 初始净值
   */
  beginNet: number;

  /**
   * 卡片编号
   */
  cardCode: number;

  /**
   * 清理成本
   */
  clearCost: number;

  /**
   * 清理日期
   */
  clearDate: string;

  /**
   * 清理收入
   */
  clearIncome: number;

  /**
   * 清理原因
   */
  clearReason: string;

  /**
   * 清理增值税
   */
  clearVat: number;

  /**
   * 其他减值科目
   */
  decrementOtherSubject: string;

  /**
   * 减值准备金额（元）
   */
  decrementProvisions: number;

  /**
   * 减值科目名称
   */
  decrementSubject: string;

  /**
   * 折旧方法（直线法/双倍余额递减法等）
   */
  depreciationMethod: string;

  /**
   * 折旧年限（月）
   */
  depreciationPeriod: number;

  /**
   * 折旧科目名称
   */
  depreciationSubject: string;

  /**
   * 所属部门ID
   */
  deptId: number | string;

  /**
   * 资产科目名称
   */
  assetSubject: string;

  /**
   * 增加方式
   */
  increaseType: string;

  /**
   * 录入时间
   */
  inputTime: string;

  /**
   * 年份
   */
  accountYear: number;

  /**
   * 月份
   */
  accountMonth: number;

  /**
   * 本月折旧金额
   */
  monthDepreciation: number;

  /**
   * 净值
   */
  netValue: number;

  /**
   * 备注
   */
  note: string;

  /**
   * 原始价值
   */
  originalValue: number;

  /**
   * 购置日期
   */
  purchaseDate: string;

  /**
   * 残值率（%）
   */
  residualRate: number | string;

  /**
   * 残值
   */
  residualValue: number | string;

  /**
   * 存放地点
   */
  storePlace: string;

  /**
   * 本月折旧金额
   */
  thisMonthDepreciation: number;

  /**
   * 总价值
   */
  totalValue: number;

  /**
   * 使用情况
   */
  usageInfo: string;

  /**
   * 增值税
   */
  valueAddedTax: number;

  /**
   * 数量
   */
  count: number;
}

export interface FixedAssetForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 累计减值金额
   */
  accumulatedDecrement?: number;

  /**
   * 累计折旧科目名称
   */
  accumulatedDepreciationSubject?: string;

  /**
   * 累计折旧金额
   */
  accumulatedDepreciation?: number;

  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 资产类型
   */
  assetType?: string;

  /**
   * 开始折旧金额
   */
  beginDepreciation?: number;

  /**
   * 原始开始折旧金额
   */
  originalBeginDepreciation?: number;

  /**
   * 初始净值
   */
  beginNet?: number;

  /**
   * 卡片编号
   */
  cardCode?: number;

  /**
   * 清理成本
   */
  clearCost?: number;

  /**
   * 清理日期
   */
  clearDate?: string;

  /**
   * 清理收入
   */
  clearIncome?: number;

  /**
   * 清理原因
   */
  clearReason?: string;

  /**
   * 清理增值税
   */
  clearVat?: number;

  /**
   * 其他减值科目
   */
  decrementOtherSubject?: string;

  /**
   * 减值准备金额（元）
   */
  decrementProvisions?: number;

  /**
   * 减值科目名称
   */
  decrementSubject?: string;

  /**
   * 折旧方法（直线法/双倍余额递减法等）
   */
  depreciationMethod?: string;

  /**
   * 折旧年限（月）
   */
  depreciationPeriod?: number;

  /**
   * 折旧科目名称
   */
  depreciationSubject?: string;

  /**
   * 所属部门ID
   */
  deptId?: number | string;

  /**
   * 资产科目名称
   */
  assetSubject?: string;

  /**
   * 增加方式
   */
  increaseType?: string;

  /**
   * 录入时间
   */
  inputTime?: string;

  /**
   * 年份
   */
  accountYear?: number;

  /**
   * 月份
   */
  accountMonth?: number;

  /**
   * 本月折旧金额
   */
  monthDepreciation?: number;

  /**
   * 净值
   */
  netValue?: number;

  /**
   * 备注
   */
  note?: string;

  /**
   * 原始价值
   */
  originalValue?: number;

  /**
   * 购置日期
   */
  purchaseDate?: string;

  /**
   * 残值率（%）
   */
  residualRate?: number | string;

  /**
   * 残值
   */
  residualValue?: number | string;

  /**
   * 存放地点
   */
  storePlace?: string;

  /**
   * 本月折旧金额
   */
  thisMonthDepreciation?: number;

  /**
   * 总价值
   */
  totalValue?: number;

  /**
   * 使用情况
   */
  usageInfo?: string;

  /**
   * 增值税
   */
  valueAddedTax?: number;

  /**
   * 数量
   */
  count?: number;
}

export interface FixedAssetQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 累计减值金额
   */
  accumulatedDecrement?: number;

  /**
   * 累计折旧科目名称
   */
  accumulatedDepreciationSubject?: string;

  /**
   * 累计折旧金额
   */
  accumulatedDepreciation?: number;

  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 资产类型
   */
  assetType?: string;

  /**
   * 开始折旧金额
   */
  beginDepreciation?: number;

  /**
   * 原始开始折旧金额
   */
  originalBeginDepreciation?: number;

  /**
   * 初始净值
   */
  beginNet?: number;

  /**
   * 卡片编号
   */
  cardCode?: number;

  /**
   * 清理成本
   */
  clearCost?: number;

  /**
   * 清理日期
   */
  clearDate?: string;

  /**
   * 清理收入
   */
  clearIncome?: number;

  /**
   * 清理原因
   */
  clearReason?: string;

  /**
   * 清理增值税
   */
  clearVat?: number;

  /**
   * 其他减值科目
   */
  decrementOtherSubject?: string;

  /**
   * 减值准备金额（元）
   */
  decrementProvisions?: number;

  /**
   * 减值科目名称
   */
  decrementSubject?: string;

  /**
   * 折旧方法（直线法/双倍余额递减法等）
   */
  depreciationMethod?: string;

  /**
   * 折旧年限（月）
   */
  depreciationPeriod?: number;

  /**
   * 折旧科目名称
   */
  depreciationSubject?: string;

  /**
   * 所属部门ID
   */
  deptId?: number | string;

  /**
   * 资产科目名称
   */
  assetSubject?: string;

  /**
   * 增加方式
   */
  increaseType?: string;

  /**
   * 录入时间
   */
  inputTime?: string;

  /**
   * 年份
   */
  accountYear?: number;

  /**
   * 月份
   */
  accountMonth?: number;

  /**
   * 本月折旧金额
   */
  monthDepreciation?: number;

  /**
   * 净值
   */
  netValue?: number;

  /**
   * 备注
   */
  note?: string;

  /**
   * 原始价值
   */
  originalValue?: number;

  /**
   * 购置日期
   */
  purchaseDate?: string;

  /**
   * 残值率（%）
   */
  residualRate?: number | string;

  /**
   * 残值
   */
  residualValue?: number | string;

  /**
   * 存放地点
   */
  storePlace?: string;

  /**
   * 本月折旧金额
   */
  thisMonthDepreciation?: number;

  /**
   * 总价值
   */
  totalValue?: number;

  /**
   * 使用情况
   */
  usageInfo?: string;

  /**
   * 增值税
   */
  valueAddedTax?: number;

  /**
   * 数量
   */
  count?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}

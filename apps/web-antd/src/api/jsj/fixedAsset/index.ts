import type { FixedAssetForm, FixedAssetQuery, FixedAssetVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询固定资产列表
 * @param params
 * @returns 固定资产列表
 */
export function fixedAssetList(params?: FixedAssetQuery) {
  return requestClient.get<PageResult<FixedAssetVO>>('/jsj/fixedAsset/list', {
    params,
  });
}

/**
 * 导出固定资产列表
 * @param params
 * @returns 固定资产列表
 */
export function fixedAssetExport(params?: FixedAssetQuery) {
  return commonExport('/jsj/fixedAsset/export', params ?? {});
}

/**
 * 查询固定资产详情
 * @param id id
 * @returns 固定资产详情
 */
export function fixedAssetInfo(id: ID) {
  return requestClient.get<FixedAssetVO>(`/jsj/fixedAsset/${id}`);
}

/**
 * 新增固定资产
 * @param data
 * @returns void
 */
export function fixedAssetAdd(data: FixedAssetForm) {
  return requestClient.postWithMsg<void>('/jsj/fixedAsset', data);
}

/**
 * 更新固定资产
 * @param data
 * @returns void
 */
export function fixedAssetUpdate(data: FixedAssetForm) {
  return requestClient.putWithMsg<void>('/jsj/fixedAsset', data);
}

/**
 * 删除固定资产
 * @param id id
 * @returns void
 */
export function fixedAssetRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/fixedAsset/${id}`);
}

import type { BaseEntity, PageQuery } from '#/api/common';

export interface VoucherVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 附件数量
   */
  attachNum: number;

  /**
   * 审核意见
   */
  auditComment: string;

  /**
   * 审核人ID
   */
  auditorId: number | string;

  /**
   * 贷方总额
   */
  creditTotal: number;

  /**
   * 借方总额
   */
  debitTotal: number;

  /**
   * 凭证日期
   */
  voucherDate: string;

  /**
   * 所属月份
   */
  voucherMonth: number;

  /**
   * 凭证编号
   */
  voucherNo: number;

  /**
   * 凭证字
   */
  voucherWord: string;

  /**
   * 凭证所属年份
   */
  voucherYear: number;

  /**
   * 凭证类型
   */
  type: string;
}

export interface VoucherForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 附件数量
   */
  attachNum?: number;

  /**
   * 审核意见
   */
  auditComment?: string;

  /**
   * 审核人ID
   */
  auditorId?: number | string;

  /**
   * 贷方总额
   */
  creditTotal?: number;

  /**
   * 借方总额
   */
  debitTotal?: number;

  /**
   * 凭证日期
   */
  voucherDate?: string;

  /**
   * 所属月份
   */
  voucherMonth?: number;

  /**
   * 凭证编号
   */
  voucherNo?: number;

  /**
   * 凭证字
   */
  voucherWord?: string;

  /**
   * 凭证所属年份
   */
  voucherYear?: number;

  /**
   * 凭证类型
   */
  type?: string;
}

export interface VoucherQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 附件数量
   */
  attachNum?: number;

  /**
   * 审核意见
   */
  auditComment?: string;

  /**
   * 审核人ID
   */
  auditorId?: number | string;

  /**
   * 贷方总额
   */
  creditTotal?: number;

  /**
   * 借方总额
   */
  debitTotal?: number;

  /**
   * 凭证日期
   */
  voucherDate?: string;

  /**
   * 所属月份
   */
  voucherMonth?: number;

  /**
   * 凭证编号
   */
  voucherNo?: number;

  /**
   * 凭证字
   */
  voucherWord?: string;

  /**
   * 凭证所属年份
   */
  voucherYear?: number;

  /**
   * 凭证类型
   */
  type?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

import type { VoucherForm, VoucherQuery, VoucherVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询凭证列表
 * @param params
 * @returns 凭证列表
 */
export function voucherList(params?: VoucherQuery) {
  return requestClient.get<PageResult<VoucherVO>>('/jsj/voucher/list', {
    params,
  });
}

/**
 * 导出凭证列表
 * @param params
 * @returns 凭证列表
 */
export function voucherExport(params?: VoucherQuery) {
  return commonExport('/jsj/voucher/export', params ?? {});
}

/**
 * 查询凭证详情
 * @param id id
 * @returns 凭证详情
 */
export function voucherInfo(id: ID) {
  return requestClient.get<VoucherVO>(`/jsj/voucher/${id}`);
}

/**
 * 新增凭证
 * @param data
 * @returns void
 */
export function voucherAdd(data: VoucherForm) {
  return requestClient.postWithMsg<void>('/jsj/voucher', data);
}

/**
 * 更新凭证
 * @param data
 * @returns void
 */
export function voucherUpdate(data: VoucherForm) {
  return requestClient.putWithMsg<void>('/jsj/voucher', data);
}

/**
 * 删除凭证
 * @param id id
 * @returns void
 */
export function voucherRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/voucher/${id}`);
}

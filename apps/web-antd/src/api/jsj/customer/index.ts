import type { CustomerForm, CustomerQuery, CustomerVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询客户列表
 * @param params
 * @returns 客户列表
 */
export function customerList(params?: CustomerQuery) {
  return requestClient.get<PageResult<CustomerVO>>('/jsj/customer/list', {
    params,
  });
}

/**
 * 导出客户列表
 * @param params
 * @returns 客户列表
 */
export function customerExport(params?: CustomerQuery) {
  return commonExport('/jsj/customer/export', params ?? {});
}

/**
 * 查询客户详情
 * @param customerId id
 * @returns 客户详情
 */
export function customerInfo(customerId: ID) {
  return requestClient.get<CustomerVO>(`/jsj/customer/${customerId}`);
}

/**
 * 新增客户
 * @param data
 * @returns void
 */
export function customerAdd(data: CustomerForm) {
  return requestClient.postWithMsg<void>('/jsj/customer', data);
}

/**
 * 更新客户
 * @param data
 * @returns void
 */
export function customerUpdate(data: CustomerForm) {
  return requestClient.putWithMsg<void>('/jsj/customer', data);
}

/**
 * 删除客户
 * @param customerId id
 * @returns void
 */
export function customerRemove(customerId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/customer/${customerId}`);
}

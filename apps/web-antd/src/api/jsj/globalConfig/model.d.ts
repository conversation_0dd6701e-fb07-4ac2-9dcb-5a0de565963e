import type { BaseEntity, PageQuery } from '#/api/common';

export interface GlobalConfigVO {
  /**
   * 参数主键
   */
  configId: number | string;

  /**
   * 参数名称
   */
  configType: string;

  /**
   * 参数键名
   */
  configKey: string;

  /**
   * 参数键值
   */
  configValue: string;

  /**
   * 备注
   */
  remark: string;
}

export interface GlobalConfigForm extends BaseEntity {
  /**
   * 参数主键
   */
  configId?: number | string;

  /**
   * 参数名称
   */
  configType?: string;

  /**
   * 参数键名
   */
  configKey?: string;

  /**
   * 参数键值
   */
  configValue?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface GlobalConfigQuery extends PageQuery {
  /**
   * 参数名称
   */
  configType?: string;

  /**
   * 参数键名
   */
  configKey?: string;

  /**
   * 参数键值
   */
  configValue?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

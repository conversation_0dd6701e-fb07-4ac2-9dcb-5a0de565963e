import type {
  CustomerInvoiceDetailForm,
  CustomerInvoiceDetailQuery,
  CustomerInvoiceDetailVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询客户发票明细列表
 * @param params
 * @returns 客户发票明细列表
 */
export function customerInvoiceDetailList(params?: CustomerInvoiceDetailQuery) {
  return requestClient.get<PageResult<CustomerInvoiceDetailVO>>(
    '/jsj/customerInvoiceDetail/list',
    { params },
  );
}

/**
 * 导出客户发票明细列表
 * @param params
 * @returns 客户发票明细列表
 */
export function customerInvoiceDetailExport(
  params?: CustomerInvoiceDetailQuery,
) {
  return commonExport('/jsj/customerInvoiceDetail/export', params ?? {});
}

/**
 * 查询客户发票明细详情
 * @param invoiceDetailId id
 * @returns 客户发票明细详情
 */
export function customerInvoiceDetailInfo(invoiceDetailId: ID) {
  return requestClient.get<CustomerInvoiceDetailVO>(
    `/jsj/customerInvoiceDetail/${invoiceDetailId}`,
  );
}

/**
 * 新增客户发票明细
 * @param data
 * @returns void
 */
export function customerInvoiceDetailAdd(data: CustomerInvoiceDetailForm) {
  return requestClient.postWithMsg<void>('/jsj/customerInvoiceDetail', data);
}

/**
 * 更新客户发票明细
 * @param data
 * @returns void
 */
export function customerInvoiceDetailUpdate(data: CustomerInvoiceDetailForm) {
  return requestClient.putWithMsg<void>('/jsj/customerInvoiceDetail', data);
}

/**
 * 删除客户发票明细
 * @param invoiceDetailId id
 * @returns void
 */
export function customerInvoiceDetailRemove(invoiceDetailId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(
    `/jsj/customerInvoiceDetail/${invoiceDetailId}`,
  );
}

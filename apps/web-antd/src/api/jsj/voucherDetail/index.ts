import type {
  VoucherDetailForm,
  VoucherDetailQuery,
  VoucherDetailVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询凭证详情列表
 * @param params
 * @returns 凭证详情列表
 */
export function voucherDetailList(params?: VoucherDetailQuery) {
  return requestClient.get<PageResult<VoucherDetailVO>>(
    '/jsj/voucherDetail/list',
    { params },
  );
}

/**
 * 导出凭证详情列表
 * @param params
 * @returns 凭证详情列表
 */
export function voucherDetailExport(params?: VoucherDetailQuery) {
  return commonExport('/jsj/voucherDetail/export', params ?? {});
}

/**
 * 查询凭证详情详情
 * @param id id
 * @returns 凭证详情详情
 */
export function voucherDetailInfo(id: ID) {
  return requestClient.get<VoucherDetailVO>(`/jsj/voucherDetail/${id}`);
}

/**
 * 新增凭证详情
 * @param data
 * @returns void
 */
export function voucherDetailAdd(data: VoucherDetailForm) {
  return requestClient.postWithMsg<void>('/jsj/voucherDetail', data);
}

/**
 * 更新凭证详情
 * @param data
 * @returns void
 */
export function voucherDetailUpdate(data: VoucherDetailForm) {
  return requestClient.putWithMsg<void>('/jsj/voucherDetail', data);
}

/**
 * 删除凭证详情
 * @param id id
 * @returns void
 */
export function voucherDetailRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/voucherDetail/${id}`);
}

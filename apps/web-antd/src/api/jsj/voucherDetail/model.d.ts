import type { BaseEntity, PageQuery } from '#/api/common';

export interface VoucherDetailVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 凭证ID
   */
  voucherId: number | string;

  /**
   * 会计科目
   */
  subject: string;

  /**
   * 备注（补充说明）
   */
  comment: string;

  /**
   * 贷方金额
   */
  credit: number;

  /**
   * 借方金额
   */
  debit: number;

  /**
   * 摘要
   */
  summary: string;

  /**
   * 排序索引
   */
  idx: number | string;

  /**
   * 科目id
   */
  subjectId: number | string;

  /**
   * 辅助核算
   */
  auxiliary: string;
}

export interface VoucherDetailForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 凭证ID
   */
  voucherId?: number | string;

  /**
   * 会计科目
   */
  subject?: string;

  /**
   * 备注（补充说明）
   */
  comment?: string;

  /**
   * 贷方金额
   */
  credit?: number;

  /**
   * 借方金额
   */
  debit?: number;

  /**
   * 摘要
   */
  summary?: string;

  /**
   * 排序索引
   */
  idx?: number | string;

  /**
   * 科目id
   */
  subjectId?: number | string;

  /**
   * 辅助核算
   */
  auxiliary?: string;
}

export interface VoucherDetailQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 凭证ID
   */
  voucherId?: number | string;

  /**
   * 会计科目
   */
  subject?: string;

  /**
   * 备注（补充说明）
   */
  comment?: string;

  /**
   * 贷方金额
   */
  credit?: number;

  /**
   * 借方金额
   */
  debit?: number;

  /**
   * 摘要
   */
  summary?: string;

  /**
   * 排序索引
   */
  idx?: number | string;

  /**
   * 科目id
   */
  subjectId?: number | string;

  /**
   * 辅助核算
   */
  auxiliary?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

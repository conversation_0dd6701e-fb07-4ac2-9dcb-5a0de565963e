import type {
  AccountingEntriesForm,
  AccountingEntriesQuery,
  AccountingEntriesVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询会计分录列表
 * @param params
 * @returns 会计分录列表
 */
export function accountingEntriesList(params?: AccountingEntriesQuery) {
  return requestClient.get<PageResult<AccountingEntriesVO>>(
    '/jsj/accountingEntries/list',
    { params },
  );
}

/**
 * 导出会计分录列表
 * @param params
 * @returns 会计分录列表
 */
export function accountingEntriesExport(params?: AccountingEntriesQuery) {
  return commonExport('/jsj/accountingEntries/export', params ?? {});
}

/**
 * 查询会计分录详情
 * @param id id
 * @returns 会计分录详情
 */
export function accountingEntriesInfo(id: ID) {
  return requestClient.get<AccountingEntriesVO>(`/jsj/accountingEntries/${id}`);
}

/**
 * 新增会计分录
 * @param data
 * @returns void
 */
export function accountingEntriesAdd(data: AccountingEntriesForm) {
  return requestClient.postWithMsg<void>('/jsj/accountingEntries', data);
}

/**
 * 更新会计分录
 * @param data
 * @returns void
 */
export function accountingEntriesUpdate(data: AccountingEntriesForm) {
  return requestClient.putWithMsg<void>('/jsj/accountingEntries', data);
}

/**
 * 删除会计分录
 * @param id id
 * @returns void
 */
export function accountingEntriesRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/accountingEntries/${id}`);
}

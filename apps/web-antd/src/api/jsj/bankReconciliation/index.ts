import type {
  BankReconciliationForm,
  BankReconciliationQuery,
  BankReconciliationVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询银行对账单列表
 * @param params
 * @returns 银行对账单列表
 */
export function bankReconciliationList(params?: BankReconciliationQuery) {
  return requestClient.get<PageResult<BankReconciliationVO>>(
    '/jsj/bankReconciliation/list',
    { params },
  );
}

/**
 * 导出银行对账单列表
 * @param params
 * @returns 银行对账单列表
 */
export function bankReconciliationExport(params?: BankReconciliationQuery) {
  return commonExport('/jsj/bankReconciliation/export', params ?? {});
}

/**
 * 查询银行对账单详情
 * @param id id
 * @returns 银行对账单详情
 */
export function bankReconciliationInfo(id: ID) {
  return requestClient.get<BankReconciliationVO>(
    `/jsj/bankReconciliation/${id}`,
  );
}

/**
 * 新增银行对账单
 * @param data
 * @returns void
 */
export function bankReconciliationAdd(data: BankReconciliationForm) {
  return requestClient.postWithMsg<void>('/jsj/bankReconciliation', data);
}

/**
 * 更新银行对账单
 * @param data
 * @returns void
 */
export function bankReconciliationUpdate(data: BankReconciliationForm) {
  return requestClient.putWithMsg<void>('/jsj/bankReconciliation', data);
}

/**
 * 删除银行对账单
 * @param id id
 * @returns void
 */
export function bankReconciliationRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/bankReconciliation/${id}`);
}

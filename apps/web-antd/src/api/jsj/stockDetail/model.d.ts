import type { BaseEntity, PageQuery } from '#/api/common';

export interface StockDetailVO {
  /**
   *
   */
  id: number | string;

  /**
   * 库存关联ID
   */
  stockId: number | string;

  /**
   * 数量
   */
  num: number;

  /**
   * 单价
   */
  price: number;

  /**
   * 不含税金额
   */
  notTaxMoney: number;

  /**
   * 税率
   */
  taxRate: number;

  /**
   * 税额
   */
  taxMoney: number;

  /**
   * 价税合计
   */
  totalMoneyAmount: number;
}

export interface StockDetailForm extends BaseEntity {
  /**
   *
   */
  id?: number | string;

  /**
   * 库存关联ID
   */
  stockId?: number | string;

  /**
   * 数量
   */
  num?: number;

  /**
   * 单价
   */
  price?: number;

  /**
   * 不含税金额
   */
  notTaxMoney?: number;

  /**
   * 税率
   */
  taxRate?: number;

  /**
   * 税额
   */
  taxMoney?: number;

  /**
   * 价税合计
   */
  totalMoneyAmount?: number;
}

export interface StockDetailQuery extends PageQuery {
  /**
   * 库存关联ID
   */
  stockId?: number | string;

  /**
   * 数量
   */
  num?: number;

  /**
   * 单价
   */
  price?: number;

  /**
   * 不含税金额
   */
  notTaxMoney?: number;

  /**
   * 税率
   */
  taxRate?: number;

  /**
   * 税额
   */
  taxMoney?: number;

  /**
   * 价税合计
   */
  totalMoneyAmount?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}

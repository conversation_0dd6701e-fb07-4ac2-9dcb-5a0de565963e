import type {
  CustomerInvoiceItemForm,
  CustomerInvoiceItemQuery,
  CustomerInvoiceItemVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询客户发票明细列表
 * @param params
 * @returns 客户发票明细列表
 */
export function customerInvoiceItemList(params?: CustomerInvoiceItemQuery) {
  return requestClient.get<PageResult<CustomerInvoiceItemVO>>(
    '/jsj/customerInvoiceItem/list',
    { params },
  );
}

/**
 * 导出客户发票明细列表
 * @param params
 * @returns 客户发票明细列表
 */
export function customerInvoiceItemExport(params?: CustomerInvoiceItemQuery) {
  return commonExport('/jsj/customerInvoiceItem/export', params ?? {});
}

/**
 * 查询客户发票明细详情
 * @param invoiceDetailId id
 * @returns 客户发票明细详情
 */
export function customerInvoiceItemInfo(invoiceDetailId: ID) {
  return requestClient.get<CustomerInvoiceItemVO>(
    `/jsj/customerInvoiceItem/${invoiceDetailId}`,
  );
}

/**
 * 新增客户发票明细
 * @param data
 * @returns void
 */
export function customerInvoiceItemAdd(data: CustomerInvoiceItemForm) {
  return requestClient.postWithMsg<void>('/jsj/customerInvoiceItem', data);
}

/**
 * 更新客户发票明细
 * @param data
 * @returns void
 */
export function customerInvoiceItemUpdate(data: CustomerInvoiceItemForm) {
  return requestClient.putWithMsg<void>('/jsj/customerInvoiceItem', data);
}

/**
 * 删除客户发票明细
 * @param invoiceDetailId id
 * @returns void
 */
export function customerInvoiceItemRemove(invoiceDetailId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(
    `/jsj/customerInvoiceItem/${invoiceDetailId}`,
  );
}

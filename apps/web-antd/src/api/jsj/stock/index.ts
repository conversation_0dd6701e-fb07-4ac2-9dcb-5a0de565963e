import type { StockForm, StockQuery, StockVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询库存列表
 * @param params
 * @returns 库存列表
 */
export function stockList(params?: StockQuery) {
  return requestClient.get<PageResult<StockVO>>('/jsj/stock/list', { params });
}

/**
 * 导出库存列表
 * @param params
 * @returns 库存列表
 */
export function stockExport(params?: StockQuery) {
  return commonExport('/jsj/stock/export', params ?? {});
}

/**
 * 查询库存详情
 * @param id id
 * @returns 库存详情
 */
export function stockInfo(id: ID) {
  return requestClient.get<StockVO>(`/jsj/stock/${id}`);
}

/**
 * 新增库存
 * @param data
 * @returns void
 */
export function stockAdd(data: StockForm) {
  return requestClient.postWithMsg<void>('/jsj/stock', data);
}

/**
 * 更新库存
 * @param data
 * @returns void
 */
export function stockUpdate(data: StockForm) {
  return requestClient.putWithMsg<void>('/jsj/stock', data);
}

/**
 * 删除库存
 * @param id id
 * @returns void
 */
export function stockRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/stock/${id}`);
}

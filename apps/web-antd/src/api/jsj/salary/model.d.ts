import type { BaseEntity, PageQuery } from '#/api/common';

export interface SalaryVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 工资所属年份
   */
  year: number;

  /**
   * 工资所属月份
   */
  month: number;

  /**
   * 员工ID
   */
  employeeId: number | string;

  /**
   * 基本工资
   */
  basicWage: number;

  /**
   * 出勤天数
   */
  attendanceDays: string;

  /**
   * 出勤工资
   */
  attendanceSalary: number;

  /**
   * 奖金
   */
  bonus: number;

  /**
   * 津贴
   */
  allowance: number;

  /**
   * 补贴
   */
  subsidies: number | string;

  /**
   * 扣除项
   */
  deductions: number;

  /**
   * 应发工资
   */
  income: number;

  /**
   * 养老保险个人缴纳部分
   */
  dkEndowmentInsurance: number;

  /**
   * 医疗保险个人缴纳部分
   */
  dkMedicalInsurance: number;

  /**
   * 失业保险个人缴纳部分
   */
  dkUnemploymentInsurance: number;

  /**
   * 公积金个人缴纳部分
   */
  dkHouseProvidentFund: number | string;

  /**
   * 公司代扣款项
   */
  dkCompany: number;

  /**
   * 其他代扣项
   */
  dkOther: number;

  /**
   * 实发工资
   */
  realWage: number;
}

export interface SalaryForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 工资所属年份
   */
  year?: number;

  /**
   * 工资所属月份
   */
  month?: number;

  /**
   * 员工ID
   */
  employeeId?: number | string;

  /**
   * 基本工资
   */
  basicWage?: number;

  /**
   * 出勤天数
   */
  attendanceDays?: string;

  /**
   * 出勤工资
   */
  attendanceSalary?: number;

  /**
   * 奖金
   */
  bonus?: number;

  /**
   * 津贴
   */
  allowance?: number;

  /**
   * 补贴
   */
  subsidies?: number | string;

  /**
   * 扣除项
   */
  deductions?: number;

  /**
   * 应发工资
   */
  income?: number;

  /**
   * 养老保险个人缴纳部分
   */
  dkEndowmentInsurance?: number;

  /**
   * 医疗保险个人缴纳部分
   */
  dkMedicalInsurance?: number;

  /**
   * 失业保险个人缴纳部分
   */
  dkUnemploymentInsurance?: number;

  /**
   * 公积金个人缴纳部分
   */
  dkHouseProvidentFund?: number | string;

  /**
   * 公司代扣款项
   */
  dkCompany?: number;

  /**
   * 其他代扣项
   */
  dkOther?: number;

  /**
   * 实发工资
   */
  realWage?: number;
}

export interface SalaryQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 工资所属年份
   */
  year?: number;

  /**
   * 工资所属月份
   */
  month?: number;

  /**
   * 员工ID
   */
  employeeId?: number | string;

  /**
   * 基本工资
   */
  basicWage?: number;

  /**
   * 出勤天数
   */
  attendanceDays?: string;

  /**
   * 出勤工资
   */
  attendanceSalary?: number;

  /**
   * 奖金
   */
  bonus?: number;

  /**
   * 津贴
   */
  allowance?: number;

  /**
   * 补贴
   */
  subsidies?: number | string;

  /**
   * 扣除项
   */
  deductions?: number;

  /**
   * 应发工资
   */
  income?: number;

  /**
   * 养老保险个人缴纳部分
   */
  dkEndowmentInsurance?: number;

  /**
   * 医疗保险个人缴纳部分
   */
  dkMedicalInsurance?: number;

  /**
   * 失业保险个人缴纳部分
   */
  dkUnemploymentInsurance?: number;

  /**
   * 公积金个人缴纳部分
   */
  dkHouseProvidentFund?: number | string;

  /**
   * 公司代扣款项
   */
  dkCompany?: number;

  /**
   * 其他代扣项
   */
  dkOther?: number;

  /**
   * 实发工资
   */
  realWage?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}

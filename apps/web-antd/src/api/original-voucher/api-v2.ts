import { RequestClient } from '@vben/request';
import type {
  AccountSubjectQueryParams,
  AccountSubjectResponse,
  AssistantAccountingQueryParams,
  AssistantAccountingResponse,
  BankReceiptData,
  BankReceiptQueryParams,
  BankReceiptUpdateSceneParams,
  BatchUpdateResponse,
  BusinessApiResponse,
  CompaniesFullApiResponse,
  CompaniesQueryParams,
  CompanyData,
  FullApiResponse,
  InvoiceData,
  InvoiceQueryParams,
  InvoiceUpdateSceneParams,
  PayrollData,
  PayrollQueryParams,
  ScenarioConditionAddParams,
  ScenarioConditionAddResponse,
  ScenarioConditionConfigData,
  ScenarioConditionConfigQueryParams,

  ScenarioConditionData,
  ScenarioConditionDeleteParams,
  ScenarioConditionDeleteResponse,
  ScenarioConditionQueryParams,
  ScenarioConditionUpdateParams,
  ScenarioConditionUpdateResponse,
  ScenarioEntryAddParams,
  ScenarioEntryAddResponse,
  ScenarioEntryData,
  ScenarioEntryDeleteParams,
  ScenarioEntryDeleteResponse,
  ScenarioEntryQueryParams,
  ScenarioEntryUpdateParams,
  ScenarioEntryUpdateResponse,
  UpdateAutoModeParams,
  UpdateAutoModeResponse,
  VoucherPdfGenerateParams,
  VoucherPdfGenerateResponse,
  VoucherPdfFullApiResponse,
  VoucherQueryParams,
  VoucherBusinessApiResponse,
  VoucherSourceDataQueryParams,
  VoucherSourceDataResponse,
  UpdateVoucherParams,
  UpdateVoucherResponse,
  MergeVouchersParams,
  MergeVouchersResponse,
  GeneralMergeVouchersParams,
  GeneralMergeVouchersResponse,
  WriteBackVouchersParams,
  WriteBackVouchersResponse,
  UserCustomerNamesQueryParams,
  UserCustomerNamesResponseData,
  DeleteVouchersParams,
  DeleteVouchersResponse,
} from './types';

// 创建专用的请求客户端，使用完整的API路径
const requestClient = new RequestClient({
  baseURL: '/prod-api/autojob/api',
  timeout: 10000,
});



/**
 * 查询发票列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 发票列表
 */
export function getInvoiceListV2(params: InvoiceQueryParams) {
  return requestClient.get<FullApiResponse<InvoiceData>>('/invoice/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询银行回单列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 银行回单列表
 */
export function getBankReceiptListV2(params: BankReceiptQueryParams) {
  return requestClient.get<FullApiResponse<BankReceiptData>>('/bank_receipts/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询工资单列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 工资单列表
 */
export function getPayrollListV2(params: PayrollQueryParams) {
  return requestClient.get<FullApiResponse<PayrollData>>('/payroll/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 处理API响应，提取业务数据
 * @param response 完整的API响应
 * @returns 业务数据
 */
export function extractBusinessData<T>(response: FullApiResponse<T>): BusinessApiResponse<T> {
  return response.data;
}

/**
 * 检查业务响应是否成功
 * @param businessResponse 业务响应
 * @returns 是否成功
 */
export function isBusinessSuccess<T>(businessResponse: BusinessApiResponse<T>): boolean {
  return businessResponse.status === 'success';
}

/**
 * 获取业务数据数组
 * @param businessResponse 业务响应
 * @returns 数据数组
 */
export function getBusinessDataArray<T>(businessResponse: BusinessApiResponse<T>): T[] {
  return businessResponse.data || [];
}

/**
 * 统一的API调用处理函数
 * @param apiCall API调用函数
 * @returns 处理后的业务数据
 */
export async function handleApiCall<T>(
  apiCall: () => Promise<FullApiResponse<T>>
): Promise<{
  success: boolean;
  data: T[];
  message: string;
  originalResponse?: FullApiResponse<T>;
}> {
  try {
    const response = await apiCall();
    const businessData = extractBusinessData(response);
    
    return {
      success: isBusinessSuccess(businessData),
      data: getBusinessDataArray(businessData),
      message: businessData.message || '操作成功',
      originalResponse: response,
    };
  } catch (error: any) {
    console.error('API调用失败:', error);
    return {
      success: false,
      data: [],
      message: error?.message || '请求失败',
    };
  }
}

/**
 * 查询发票列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的发票数据
 */
export async function fetchInvoiceList(params: InvoiceQueryParams) {
  return handleApiCall(() => getInvoiceListV2(params));
}

/**
 * 查询银行回单列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的银行回单数据
 */
export async function fetchBankReceiptList(params: BankReceiptQueryParams) {
  return handleApiCall(() => getBankReceiptListV2(params));
}

/**
 * 查询工资单列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的工资单数据
 */
export async function fetchPayrollList(params: PayrollQueryParams) {
  return handleApiCall(() => getPayrollListV2(params));
}

/**
 * 查询场景分录列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 场景分录列表
 */
export function getScenarioEntryListV2(params: ScenarioEntryQueryParams) {
  return requestClient.get<FullApiResponse<ScenarioEntryData>>('/scenario_entry/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询场景分录列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的场景分录数据
 */
export async function fetchScenarioEntryList(params: ScenarioEntryQueryParams) {
  return handleApiCall(() => getScenarioEntryListV2(params));
}

/**
 * 更新场景条目 (V2 - 处理实际的嵌套响应结构)
 * @param params 场景条目更新参数
 * @returns 更新结果
 */
export function updateScenarioEntryV2(params: ScenarioEntryUpdateParams) {
  return requestClient.post<FullApiResponse<ScenarioEntryUpdateResponse>>('/scenario_entry/update', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 更新场景条目的便捷方法
 * @param params 场景条目更新参数
 * @returns 处理后的更新结果
 */
export async function updateScenarioEntry(params: ScenarioEntryUpdateParams) {
  return handleApiCall(() => updateScenarioEntryV2(params));
}

/**
 * 查询场景条件列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 场景条件列表
 */
export function getScenarioConditionListV2(params: ScenarioConditionQueryParams) {
  return requestClient.get<FullApiResponse<ScenarioConditionData>>('/scenario_condition/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询场景条件列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的场景条件数据
 */
export async function fetchScenarioConditionList(params: ScenarioConditionQueryParams) {
  return handleApiCall(() => getScenarioConditionListV2(params));
}

/**
 * 添加场景条件 (V2 - 处理实际的嵌套响应结构)
 * @param params 场景条件添加参数
 * @returns 添加结果
 */
export function addScenarioConditionV2(params: ScenarioConditionAddParams) {
  return requestClient.post<FullApiResponse<ScenarioConditionAddResponse>>('/accounting_scenario_condition/add', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 添加场景条件的便捷方法
 * @param params 场景条件添加参数
 * @returns 处理后的添加结果
 */
export async function addScenarioCondition(params: ScenarioConditionAddParams) {
  return handleApiCall(() => addScenarioConditionV2(params));
}

/**
 * 添加场景分录 (V2 - 处理实际的嵌套响应结构)
 * @param params 场景分录添加参数
 * @returns 添加结果
 */
export function addScenarioEntryV2(params: ScenarioEntryAddParams) {
  return requestClient.post<FullApiResponse<ScenarioEntryAddResponse>>('/scenario_entry/add', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 添加场景分录的便捷方法
 * @param params 场景分录添加参数
 * @returns 处理后的添加结果
 */
export async function addScenarioEntry(params: ScenarioEntryAddParams) {
  return handleApiCall(() => addScenarioEntryV2(params));
}

/**
 * 批量更新发票场景 (V2 - 处理实际的嵌套响应结构)
 * @param params 发票更新场景参数数组
 * @returns 更新结果
 */
export function updateInvoiceSceneV2(params: InvoiceUpdateSceneParams[]) {
  return requestClient.post<FullApiResponse<BatchUpdateResponse>>('/invoice_service/update_scene', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 批量更新发票场景的便捷方法
 * @param params 发票更新场景参数数组
 * @returns 处理后的更新结果
 */
export async function updateInvoiceScene(params: InvoiceUpdateSceneParams[]) {
  return handleApiCall(() => updateInvoiceSceneV2(params));
}

/**
 * 批量更新银行流水场景 (V2 - 处理实际的嵌套响应结构)
 * @param params 银行流水更新场景参数数组
 * @returns 更新结果
 */
export function updateBankReceiptSceneV2(params: BankReceiptUpdateSceneParams[]) {
  return requestClient.post<FullApiResponse<BatchUpdateResponse>>('/bank_receipt/update_scene', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 批量更新银行流水场景的便捷方法
 * @param params 银行流水更新场景参数数组
 * @returns 处理后的更新结果
 */
export async function updateBankReceiptScene(params: BankReceiptUpdateSceneParams[]) {
  return handleApiCall(() => updateBankReceiptSceneV2(params));
}

/**
 * 查询科目列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 科目列表
 */
export function getAccountSubjectListV2(params: AccountSubjectQueryParams) {
  return requestClient.get<FullApiResponse<AccountSubjectResponse>>('/account-subjects/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询科目列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的科目数据
 */
export async function fetchAccountSubjectList(params: AccountSubjectQueryParams) {
  return handleApiCall(() => getAccountSubjectListV2(params));
}

/**
 * 查询辅助核算列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 辅助核算列表
 */
export function getAssistantAccountingListV2(params: AssistantAccountingQueryParams) {
  return requestClient.get<FullApiResponse<AssistantAccountingResponse>>('/assistant-accounting/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询辅助核算列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的辅助核算数据
 */
export async function fetchAssistantAccountingList(params: AssistantAccountingQueryParams) {
  return handleApiCall(() => getAssistantAccountingListV2(params));
}

/**
 * 删除场景条件 (V2 - 处理实际的嵌套响应结构)
 * @param params 场景条件删除参数
 * @returns 删除结果
 */
export function deleteScenarioConditionV2(params: ScenarioConditionDeleteParams) {
  return requestClient.get<FullApiResponse<ScenarioConditionDeleteResponse>>('/scenario_condition/delete', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 删除场景条件的便捷方法
 * @param params 场景条件删除参数
 * @returns 处理后的删除结果
 */
export async function deleteScenarioCondition(params: ScenarioConditionDeleteParams) {
  return handleApiCall(() => deleteScenarioConditionV2(params));
}

/**
 * 更新场景条件 (V2 - 处理实际的嵌套响应结构)
 * @param params 场景条件更新参数
 * @returns 更新结果
 */
export function updateScenarioConditionV2(params: ScenarioConditionUpdateParams) {
  return requestClient.post<FullApiResponse<ScenarioConditionUpdateResponse>>('/scenario_condition/update', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 更新场景条件的便捷方法
 * @param params 场景条件更新参数
 * @returns 处理后的更新结果
 */
export async function updateScenarioCondition(params: ScenarioConditionUpdateParams) {
  return handleApiCall(() => updateScenarioConditionV2(params));
}

/**
 * 删除场景分录 (V2 - 处理实际的嵌套响应结构)
 * @param params 场景分录删除参数
 * @returns 删除结果
 */
export function deleteScenarioEntryV2(params: ScenarioEntryDeleteParams) {
  return requestClient.get<FullApiResponse<ScenarioEntryDeleteResponse>>('/scenario_entry/delete', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 删除场景分录的便捷方法
 * @param params 场景分录删除参数
 * @returns 处理后的删除结果
 */
export async function deleteScenarioEntry(params: ScenarioEntryDeleteParams) {
  return handleApiCall(() => deleteScenarioEntryV2(params));
}

/**
 * 查询公司列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 公司列表
 */
export function getCompaniesListV2(params: CompaniesQueryParams) {
  return requestClient.get<CompaniesFullApiResponse>('/companies/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询公司列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的公司数据
 */
export async function fetchCompaniesList(params: CompaniesQueryParams): Promise<{
  success: boolean;
  data: CompanyData[];
  message: string;
  originalResponse?: CompaniesFullApiResponse;
}> {
  try {
    const response = await getCompaniesListV2(params);
    const businessData = response.data; // 直接获取业务数据

    return {
      success: businessData.status === 'success',
      data: businessData.data?.companies || [],
      message: businessData.message || '操作成功',
      originalResponse: response,
    };
  } catch (error: any) {
    console.error('API调用失败:', error);
    return {
      success: false,
      data: [],
      message: error?.message || '请求失败',
    };
  }
}

/**
 * 更新AI自动模式 (V2 - 处理实际的嵌套响应结构)
 * @param params 更新参数
 * @returns 更新结果
 */
export function updateAutoModeV2(params: UpdateAutoModeParams) {
  return requestClient.post<FullApiResponse<UpdateAutoModeResponse>>('/companies/update-auto-mode', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 更新AI自动模式的便捷方法
 * @param params 更新参数
 * @returns 处理后的更新结果
 */
export async function updateAutoMode(params: UpdateAutoModeParams) {
  return handleApiCall(() => updateAutoModeV2(params));
}

/**
 * 生成凭证PDF (V2 - 处理实际的嵌套响应结构)
 * @param params 凭证PDF生成参数
 * @returns PDF生成结果
 */
export function generateVoucherPdfV2(params: VoucherPdfGenerateParams) {
  return requestClient.post<VoucherPdfFullApiResponse>('/voucher/generate_pdf', params, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 生成凭证PDF的便捷方法
 * @param params 凭证PDF生成参数
 * @returns 处理后的PDF生成结果
 */
export async function generateVoucherPdf(params: VoucherPdfGenerateParams): Promise<{
  success: boolean;
  data: VoucherPdfGenerateResponse[];
  message: string;
  originalResponse?: VoucherPdfFullApiResponse;
}> {
  try {
    const response = await generateVoucherPdfV2(params);
    const businessData = response.data; // 直接获取业务数据

    return {
      success: businessData.result === 'success',
      data: [businessData], // 包装成数组格式以保持一致性
      message: businessData.result === 'success' ? '凭证PDF生成成功' : '凭证PDF生成失败',
      originalResponse: response,
    };
  } catch (error: any) {
    console.error('凭证PDF生成失败:', error);
    return {
      success: false,
      data: [],
      message: error?.message || '凭证PDF生成失败',
    };
  }
}

/**
 * 获取当前公司某个月份的凭证信息 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 凭证信息
 */
export function getCurrentVouchersV2(params: VoucherQueryParams) {
  return requestClient.get<FullApiResponse<VoucherBusinessApiResponse>>('/vouchers/current', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 获取当前公司某个月份的凭证信息的便捷方法
 * @param params 查询参数
 * @returns 处理后的凭证数据
 */
export async function getCurrentVouchers(params: VoucherQueryParams) {
  return handleApiCall(() => getCurrentVouchersV2(params));
}

/**
 * 获取凭证原始数据 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 凭证原始数据
 */
export function getVoucherSourceDataV2(params: VoucherSourceDataQueryParams) {
  return requestClient.get<FullApiResponse<VoucherSourceDataResponse>>('/vouchers/source-data', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 获取凭证原始数据的便捷方法
 * @param params 查询参数
 * @returns 处理后的凭证原始数据
 */
export async function getVoucherSourceData(params: VoucherSourceDataQueryParams) {
  return handleApiCall(() => getVoucherSourceDataV2(params));
}

/**
 * 修改凭证信息 (V2 - 处理实际的嵌套响应结构)
 * @param data 修改凭证请求数据
 * @returns 修改结果
 */
export function updateVoucherV2(data: UpdateVoucherParams) {
  return requestClient.put<FullApiResponse<UpdateVoucherResponse>>('/vouchers/update', data, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 修改凭证信息的便捷方法
 * @param data 修改凭证请求数据
 * @returns 处理后的修改结果
 */
export async function updateVoucher(data: UpdateVoucherParams) {
  return handleApiCall(() => updateVoucherV2(data));
}

/**
 * 合并凭证 (V2 - 处理实际的嵌套响应结构)
 * @param data 合并凭证请求数据
 * @returns 合并结果
 */
export function mergeVouchersV2(data: MergeVouchersParams) {
  return requestClient.post<FullApiResponse<MergeVouchersResponse>>('/vouchers/merge', data, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 合并凭证的便捷方法
 * @param data 合并凭证请求数据
 * @returns 处理后的合并结果
 */
export async function mergeVouchers(data: MergeVouchersParams) {
  return handleApiCall(() => mergeVouchersV2(data));
}

/**
 * 通用凭证合并 (V2 - 处理实际的嵌套响应结构)
 * @param data 通用凭证合并请求数据
 * @returns 合并结果
 */
export function generalMergeVouchersV2(data: GeneralMergeVouchersParams) {
  return requestClient.post<FullApiResponse<GeneralMergeVouchersResponse>>('/vouchers/general_merge', data, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 通用凭证合并的便捷方法
 * @param data 通用凭证合并请求数据
 * @returns 处理后的合并结果
 */
export async function generalMergeVouchers(data: GeneralMergeVouchersParams) {
  return handleApiCall(() => generalMergeVouchersV2(data));
}

/**
 * 凭证写入 (V2 - 处理实际的嵌套响应结构)
 * @param data 凭证写入请求数据
 * @returns 写入结果
 */
export function writeBackVouchersV2(data: WriteBackVouchersParams) {
  return requestClient.post<FullApiResponse<WriteBackVouchersResponse>>('/vouchers/write_back', data, {
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 凭证写入的便捷方法
 * @param data 凭证写入请求数据
 * @returns 处理后的写入结果
 */
export async function writeBackVouchers(data: WriteBackVouchersParams) {
  return handleApiCall(() => writeBackVouchersV2(data));
}

/**
 * 获取用户负责的客户名称列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 用户客户名称列表
 */
export function getUserCustomerNamesV2(params: UserCustomerNamesQueryParams) {
  return requestClient.get<FullApiResponse<UserCustomerNamesResponseData>>('/users/get_user_info', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 获取用户负责的客户名称列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的用户客户名称数据
 */
export async function getUserCustomerNames(params: UserCustomerNamesQueryParams) {
  return handleApiCall(() => getUserCustomerNamesV2(params));
}

/**
 * 查询场景条件配置 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 场景条件配置列表
 */
export function getScenarioConditionConfigV2(params: ScenarioConditionConfigQueryParams) {
  return requestClient.get<FullApiResponse<ScenarioConditionConfigData>>('/config/get_by_type', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询场景条件配置的便捷方法
 * @param params 查询参数
 * @returns 处理后的场景条件配置数据
 */
export async function fetchScenarioConditionConfig(params: ScenarioConditionConfigQueryParams) {
  return handleApiCall(() => getScenarioConditionConfigV2(params));
}

/**
 * 删除凭证 (V2 - 处理实际的嵌套响应结构)
 * @param data 删除凭证请求数据
 * @returns 删除结果
 */
export function deleteVouchersV2(data: DeleteVouchersParams) {
  return requestClient.delete<FullApiResponse<DeleteVouchersResponse>>('/vouchers/delete', {
    data,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 删除凭证的便捷方法
 * @param data 删除凭证请求数据
 * @returns 处理后的删除结果
 */
export async function deleteVouchers(data: DeleteVouchersParams) {
  return handleApiCall(() => deleteVouchersV2(data));
}

<script setup lang="ts">
  import type { UploadFile } from 'ant-design-vue/es/upload/interface';
  import type { SuggestionProps } from 'ant-design-x-vue';

  import type { UploadedFile } from '../types/chat';

  import { computed, h, ref } from 'vue';

  import {
    CloudDownloadOutlined,
    FileTextOutlined,
    LinkOutlined,
    MergeCellsOutlined,
    SyncOutlined,
  } from '@ant-design/icons-vue';
  import { Button } from 'ant-design-vue';
  import { Sender, Suggestion } from 'ant-design-x-vue';

  import CustomPrompts from './CustomPrompts.vue';
  import FileUpload from './FileUpload.vue';

  interface Props {
    fileItems: UploadFile[];
    fileUploadOpen: boolean;
    inputValue: string;
    loading: boolean;
    uploadedFiles: UploadedFile[];
  }

  interface Emits {
    (e: 'update:inputValue', value: string): void;
    (e: 'update:fileUploadOpen', value: boolean): void;
    (e: 'submit', content: string): void;
    (e: 'fileChange', data: { fileList: UploadFile[] }): void;
    (e: 'fileUpload', firstFile: File, fileList: FileList): void;
    (e: 'promptSelect', info: { data: any }): void;
    (e: 'suggestionSelect', itemVal: string): void;
    (e: 'syncData', content: string): void;
    (e: 'dbToVoucher', content: string): void;
    (e: 'mergeVouchers', content: string): void;
    (e: 'invoiceFileDownload', invoiceType: string): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const inputRef = ref();
  const fileUploadRef = ref();

  // 计算是否可以发送消息（有文本内容或有附件时可以发送）
  const canSendMessage = computed(() => {
    const hasContent = props.inputValue.trim().length > 0;
    const hasFiles = props.uploadedFiles.length > 0;
    return hasContent || hasFiles;
  });

  // 自定义发送按钮
  const customActions = (oriNode: any, { components }: any) => {
    const { SendButton } = components;

    return h(
      SendButton,
      {
        disabled: !canSendMessage.value,
        loading: props.loading,
        onClick: () => {
          if (canSendMessage.value) {
            handleSubmit(props.inputValue);
          }
        },
      },
      '发送',
    );
  };

  type SuggestionItems = Exclude<SuggestionProps['items'], () => void>;

  const suggestions: SuggestionItems = [
    { label: '同步数据', value: '同步数据' },
    { label: '发票生成凭证', value: '发票生成凭证' },
    { label: '凭证合并', value: '凭证合并' },
    { label: '同步发票源文件', value: '同步发票源文件' },
  ];

  const promptTemplates = [
    {
      icon: h(SyncOutlined, { style: { color: '#52C41A' } }),
      key: '1',
      label: '同步数据',
    },
    {
      icon: h(FileTextOutlined, { style: { color: '#1890FF' } }),
      key: '2',
      label: '发票生成凭证',
    },
    {
      icon: h(MergeCellsOutlined, { style: { color: '#FF6B35' } }),
      key: '3',
      label: '凭证合并',
    },
    {
      icon: h(CloudDownloadOutlined, { style: { color: '#722ED1' } }),
      key: '4',
      label: '同步发票源文件',
    },
    // {
    //   icon: h(SaveOutlined, { style: { color: '#FA8C16' } }),
    //   key: '5',
    //   label: '数据备份',
    // },
    // {
    //   icon: h(BarChartOutlined, { style: { color: '#13C2C2' } }),
    //   key: '6',
    //   label: '报表生成',
    // },
    // {
    //   icon: h(CalculatorOutlined, { style: { color: '#EB2F96' } }),
    //   key: '7',
    //   label: '税务申报',
    // },
    // {
    //   icon: h(AuditOutlined, { style: { color: '#F5222D' } }),
    //   key: '8',
    //   label: '审计报告',
    // },
  ];

  const handleSubmit = (content: string) => {
    emit('submit', content);
  };

  const handleInputChange = (value: string) => {
    emit('update:inputValue', value);
  };

  const handleFileUploadOpenChange = (open: boolean) => {
    emit('update:fileUploadOpen', open);
  };

  const handleFileChange = (data: { fileList: UploadFile[] }) => {
    emit('fileChange', data);
  };

  const handleFileUpload = (firstFile: File, fileList: FileList) => {
    emit('fileUpload', firstFile, fileList);
  };

  const handlePromptSelect = (info: { data: any }) => {
    const label = info.data.label;
    switch (label) {
      case '凭证合并': {
        // 触发凭证合并事件
        emit('mergeVouchers', '凭证合并');

        break;
      }
      case '发票生成凭证': {
        // 触发发票生成凭证事件
        emit('dbToVoucher', '发票生成凭证');

        break;
      }
      case '同步发票源文件': {
        // 触发同步发票源文件事件，同时拉取进项和销项发票
        emit('invoiceFileDownload', 'input_invoice|output_invoice');

        break;
      }
      case '同步数据': {
        // 触发同步数据事件，传递消息内容，不调用handleSubmit避免发送multi_file_processing消息
        emit('syncData', '请同步当前公司的最新数据');

        break;
      }
      case '审计报告': {
        // 触发审计报告事件
        emit('promptSelect', { data: { action: 'audit', label: '审计报告' } });

        break;
      }
      case '报表生成': {
        // 触发报表生成事件
        emit('promptSelect', { data: { action: 'report', label: '报表生成' } });

        break;
      }
      case '数据备份': {
        // 触发数据备份事件
        emit('promptSelect', { data: { action: 'backup', label: '数据备份' } });

        break;
      }
      case '税务申报': {
        // 触发税务申报事件
        emit('promptSelect', { data: { action: 'tax', label: '税务申报' } });

        break;
      }
      default: {
        emit('promptSelect', info);
      }
    }
  };

  const handleSuggestionSelect = (itemVal: string) => {
    if (itemVal === '同步发票源文件') {
      emit('invoiceFileDownload', 'input_invoice|output_invoice');
    } else {
      emit('suggestionSelect', itemVal);
    }
  };

  const handleSenderChange = (
    nextVal: string,
    { onTrigger }: { onTrigger?: (show?: boolean) => void } = {},
  ) => {
    if (nextVal === '/') {
      onTrigger?.();
    } else if (!nextVal) {
      onTrigger?.(false);
    }
    handleInputChange(nextVal);
  };

  const toggleFileUpload = () => {
    handleFileUploadOpenChange(!props.fileUploadOpen);
  };

  defineExpose({
    fileUploadRef,
    inputRef,
  });
</script>

<template>
  <div class="chat-input">
    <CustomPrompts
      :items="promptTemplates"
      :on-item-click="handlePromptSelect"
    />

    <div class="sender-container">
      <Suggestion :items="suggestions" @select="handleSuggestionSelect">
        <template #default="{ onTrigger }: any">
          <Sender
            :value="inputValue"
            ref="inputRef"
            placeholder="输入消息，按 Enter 发送..."
            :loading="loading"
            :actions="customActions"
            @submit="handleSubmit"
            @paste-file="handleFileUpload"
            @change="(val) => handleSenderChange(val, { onTrigger })"
          >
            <template #prefix>
              <Button
                type="text"
                :icon="h(LinkOutlined)"
                @click="toggleFileUpload"
              />
            </template>

            <template #header>
              <FileUpload
                ref="fileUploadRef"
                :is-open="fileUploadOpen"
                :items="fileItems"
                :loading="loading"
                @update:is-open="handleFileUploadOpenChange"
                @file-change="handleFileChange"
                @file-upload="handleFileUpload"
              />
            </template>
          </Sender>
        </template>
      </Suggestion>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .chat-input {
    .sender-container {
      width: 100%;
    }
  }
</style>

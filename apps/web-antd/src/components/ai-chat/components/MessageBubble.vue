<script setup lang="ts">
  import type { ChatMessage } from '#/store/modules/ai-chat';

  import { computed, h } from 'vue';

  import { Button } from 'ant-design-vue';

  interface Props {
    isLoading?: boolean;
    message: ChatMessage;
  }

  interface Emits {
    (e: 'show-detail', message: ChatMessage): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const showMessageDetail = () => {
    emit('show-detail', props.message);
  };

  // 创建紧凑的消息卡片组件
  const createCompactCard = (
    title: string,
    content: any,
    type: 'info' | 'success' | 'warning' = 'info',
  ) => {
    const colors = {
      info: { bg: '#e6f7ff', border: '#91d5ff', text: '#1890ff' },
      success: { bg: '#f6ffed', border: '#b7eb8f', text: '#52c41a' },
      warning: { bg: '#fffbe6', border: '#ffe58f', text: '#faad14' },
    };

    return h(
      'div',
      {
        style: {
          background: colors[type].bg,
          border: `1px solid ${colors[type].border}`,
          borderRadius: '6px',
          fontSize: '12px',
          marginBottom: '6px',
          padding: '8px 12px',
        },
      },
      [
        h(
          'div',
          {
            style: {
              color: colors[type].text,
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: '4px',
            },
          },
          title,
        ),
        h(
          'div',
          {
            style: {
              color: '#666',
              fontSize: '11px',
              lineHeight: '1.4',
            },
          },
          content,
        ),
      ],
    );
  };

  // 创建关键信息标签
  const createInfoTag = (
    label: string,
    value: number | string,
    type: 'primary' | 'success' | 'warning' = 'primary',
  ) => {
    const colors = {
      primary: { bg: '#e6f7ff', text: '#1890ff' },
      success: { bg: '#f6ffed', text: '#52c41a' },
      warning: { bg: '#fffbe6', text: '#faad14' },
    };

    return h(
      'span',
      {
        style: {
          background: colors[type].bg,
          borderRadius: '3px',
          color: colors[type].text,
          display: 'inline-block',
          fontSize: '10px',
          fontWeight: '500',
          marginBottom: '4px',
          marginRight: '6px',
          padding: '2px 6px',
        },
      },
      `${label}: ${value}`,
    );
  };

  const messageContent = computed(() => {
    const msg = props.message;

    // 银行回单类型 - 紧凑展示
    if (msg.type === 'bank_receipt_extracted' && msg.extraData?.data) {
      const data = msg.extraData.data;
      return h('div', { class: 'compact-message' }, [
        h('div', { class: 'message-title' }, msg.content),
        h('div', { class: 'info-tags' }, [
          createInfoTag('金额', `¥${data.amount}`, 'success'),
          createInfoTag('账户', data.account_name || '未知', 'primary'),
          createInfoTag('时间', data.transaction_time || '未知', 'primary'),
        ]),
        data.summary && createCompactCard('交易摘要', data.summary, 'info'),
        h('div', { class: 'message-actions' }, [
          h(
            Button,
            {
              onClick: showMessageDetail,
              size: 'small',
              type: 'link',
            },
            '查看详情',
          ),
        ]),
      ]);
    }

    // 工资单数据类型 - 紧凑展示
    if (msg.type === 'salary_data_extracted' && msg.extraData?.data) {
      const data = msg.extraData.data;
      return h('div', { class: 'compact-message' }, [
        h('div', { class: 'message-title' }, msg.content),
        h('div', { class: 'info-tags' }, [
          createInfoTag('员工数', data.employee_count || 0, 'primary'),
          createInfoTag(
            '工资总额',
            `¥${(data.total_salary || 0).toLocaleString()}`,
            'success',
          ),
          createInfoTag(
            '实发总额',
            `¥${(data.total_actual_salary || 0).toLocaleString()}`,
            'warning',
          ),
        ]),
        data.company_name &&
          createCompactCard('公司信息', data.company_name, 'info'),
        h('div', { class: 'message-actions' }, [
          h(
            Button,
            {
              onClick: showMessageDetail,
              size: 'small',
              type: 'link',
            },
            '查看详情',
          ),
        ]),
      ]);
    }

    // 凭证生成完成类型 - 紧凑展示
    if (msg.type === 'voucher_generation_completed' && msg.extraData?.data) {
      const data = msg.extraData.data;
      const summary = data.conversion_summary;
      return h('div', { class: 'compact-message' }, [
        h('div', { class: 'message-title' }, msg.content),
        summary &&
          h('div', { class: 'info-tags' }, [
            createInfoTag(
              '凭证数',
              summary.total_vouchers_generated || 0,
              'success',
            ),
            createInfoTag(
              '成功率',
              `${summary.success_rate || 0}%`,
              summary.success_rate >= 90 ? 'success' : 'warning',
            ),
            createInfoTag(
              '借方总额',
              summary.total_debit_amount || 0,
              'primary',
            ),
            createInfoTag(
              '贷方总额',
              summary.total_credit_amount || 0,
              'primary',
            ),
          ]),
        summary &&
          createCompactCard(
            '处理结果',
            `成功处理 ${summary.successful_conversions || 0} 条记录，生成 ${summary.total_vouchers_generated || 0} 张凭证`,
            'success',
          ),
        h('div', { class: 'message-actions' }, [
          h(
            Button,
            {
              onClick: showMessageDetail,
              size: 'small',
              type: 'link',
            },
            '查看详情',
          ),
        ]),
      ]);
    }

    // 任务状态类型 - 平铺展示所有步骤
    if (msg.type === 'task_status' && msg.thoughtChainItems) {
      return h('div', { class: 'compact-message task-progress' }, [
        // 步骤列表 - 平铺展示所有步骤，带流式动画效果
        h(
          'div',
          { class: 'progress-steps' },
          msg.thoughtChainItems.map((item, index) => {
            return h(
              'div',
              {
                class: ['progress-step', 'step-animate'],
                key: `step-${index}`,
                style: {
                  '--animation-delay': `${index * 200}ms`,
                },
              },
              [
                h('div', { class: 'step-content' }, [
                  item.description &&
                    h('div', { class: 'step-description' }, item.description),
                ]),
              ],
            );
          }),
        ),
      ]);
    }

    // 普通消息
    return h('div', { class: 'simple-message' }, msg.content);
  });
</script>

<template>
  <div class="message-bubble">
    <component :is="messageContent" />
  </div>
</template>

<style scoped lang="scss">
  .message-bubble {
    :deep(.ant-bubble) {
      max-width: 85%;
      margin: 6px 0;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
        transform: translateY(-1px);
      }

      &[data-type='ai'],
      &[data-type='bank_receipt_extracted'],
      &[data-type='salary_data_extracted'],
      &[data-type='voucher_generation_completed'] {
        margin-right: auto;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid rgb(255 255 255 / 60%);
        border-radius: 16px 16px 16px 4px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 6%);

        .ant-bubble-content {
          padding: 10px 12px !important;
          font-size: 12px !important;
          line-height: 1.4 !important;
          color: #333;

          // 紧凑消息样式
          .compact-message {
            .message-title {
              margin-bottom: 8px !important;
              font-size: 13px !important;
              font-weight: 600 !important;
              line-height: 1.3 !important;
              color: #1890ff !important;
            }

            .info-tags {
              margin-bottom: 8px !important;
              line-height: 1.2 !important;
            }

            .progress-info {
              font-size: 11px !important;
              line-height: 1.3 !important;
              color: #666 !important;
            }

            .message-actions {
              margin-top: 8px !important;
              text-align: right !important;

              .ant-btn-link {
                height: auto !important;
                padding: 0 4px !important;
                font-size: 11px !important;
                color: #1890ff !important;

                &:hover {
                  color: #40a9ff !important;
                }
              }
            }

            // 任务进度样式
            &.task-progress {
              .progress-steps {
                margin-top: 8px !important;

                .progress-step {
                  padding: 4px 0 !important;
                  margin-bottom: 6px !important;

                  &:last-child {
                    margin-bottom: 0 !important;
                  }

                  &.step-animate {
                    opacity: 0;
                    transform: translateY(15px);
                    animation: step-fade-in 0.6s ease-out forwards;
                    animation-delay: var(--animation-delay, 0ms);
                  }

                  .step-content {
                    .step-description {
                      font-size: 11px !important;
                      line-height: 1.3 !important;
                      color: #333 !important;
                    }
                  }
                }
              }
            }
          }

          // 简单消息样式
          .simple-message {
            font-size: 12px !important;
            line-height: 1.4 !important;
            color: #333 !important;
          }
        }
      }

      &[data-type='user'] {
        margin-left: auto;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: 1px solid rgb(255 255 255 / 20%);
        border-radius: 20px 20px 6px;
        box-shadow: 0 4px 16px rgb(102 126 234 / 30%);

        .ant-bubble-content {
          font-size: 12px !important;
          font-weight: 500;
          line-height: 1.5 !important;
          color: #fff;
        }

        .ant-bubble-meta {
          color: rgb(255 255 255 / 80%);
        }
      }

      &[data-status='loading'] {
        opacity: 0.8;
      }

      &[data-status='error'] {
        border: 1px solid #ff4d4f;
      }

      .ant-bubble-meta {
        margin-top: 3px;
        font-size: 10px;
        opacity: 0.7;
      }

      .ant-bubble-actions {
        margin-top: 6px;
        opacity: 0;
        transition: opacity 0.2s;

        .bubble-action {
          margin-right: 12px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }

      &:hover .ant-bubble-actions {
        opacity: 1;
      }
    }
  }
</style>

<!-- 全局样式覆盖 -->
<style>
  /* 流式动画关键帧 */
  @keyframes step-fade-in {
    0% {
      opacity: 0;
      transform: translateY(15px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 紧凑消息样式 */
  .ant-bubble-content .compact-message {
    font-size: 12px !important;
    line-height: 1.4 !important;
  }

  .ant-bubble-content .simple-message {
    font-size: 12px !important;
    line-height: 1.4 !important;
  }

  /* 减小气泡间距 */
  .ant-bubble {
    margin: 4px 0 !important;
  }
</style>

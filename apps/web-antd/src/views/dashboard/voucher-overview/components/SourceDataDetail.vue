<template>
  <div class="source-data-detail">
    <a-spin :spinning="loading" tip="加载详情中...">
      <div v-if="detailData" class="detail-content">
        <!-- 进项发票详情 -->
        <template v-if="voucher?.source_type === '进项发票' && detailData.invoice_detail">
          <div class="detail-section">
            <h3 class="section-title">发票基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">发票代码:</span>
                <span class="value">{{ detailData.invoice_detail.invoice_code }}</span>
              </div>
              <div class="info-item">
                <span class="label">发票号码:</span>
                <span class="value">{{ detailData.invoice_detail.invoice_number }}</span>
              </div>
              <div class="info-item">
                <span class="label">开票日期:</span>
                <span class="value">{{ detailData.invoice_detail.invoice_date }}</span>
              </div>
              <div class="info-item">
                <span class="label">销售方名称:</span>
                <span class="value">{{ detailData.invoice_detail.seller_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">销售方税号:</span>
                <span class="value">{{ detailData.invoice_detail.seller_tax_id }}</span>
              </div>
              <div class="info-item">
                <span class="label">购买方名称:</span>
                <span class="value">{{ detailData.invoice_detail.buyer_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">购买方税号:</span>
                <span class="value">{{ detailData.invoice_detail.buyer_tax_id }}</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3 class="section-title">商品明细</h3>
            <a-table 
              :columns="invoiceColumns" 
              :data-source="detailData.invoice_detail.items" 
              :pagination="false"
              size="small"
              bordered
            />
          </div>

          <div class="detail-section">
            <h3 class="section-title">金额汇总</h3>
            <div class="amount-summary">
              <div class="summary-item">
                <span class="label">合计金额:</span>
                <span class="value amount">¥{{ formatNumber(detailData.invoice_detail.total_amount) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">合计税额:</span>
                <span class="value amount">¥{{ formatNumber(detailData.invoice_detail.total_tax) }}</span>
              </div>
              <div class="summary-item total">
                <span class="label">价税合计:</span>
                <span class="value amount">¥{{ formatNumber(detailData.invoice_detail.total_with_tax) }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 销项发票详情 -->
        <template v-else-if="voucher?.source_type === '销项发票' && detailData.invoice_detail">
          <div class="detail-section">
            <h3 class="section-title">发票基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">发票代码:</span>
                <span class="value">{{ detailData.invoice_detail.invoice_code }}</span>
              </div>
              <div class="info-item">
                <span class="label">发票号码:</span>
                <span class="value">{{ detailData.invoice_detail.invoice_number }}</span>
              </div>
              <div class="info-item">
                <span class="label">开票日期:</span>
                <span class="value">{{ detailData.invoice_detail.invoice_date }}</span>
              </div>
              <div class="info-item">
                <span class="label">销售方名称:</span>
                <span class="value">{{ detailData.invoice_detail.seller_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">销售方税号:</span>
                <span class="value">{{ detailData.invoice_detail.seller_tax_id }}</span>
              </div>
              <div class="info-item">
                <span class="label">购买方名称:</span>
                <span class="value">{{ detailData.invoice_detail.buyer_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">购买方税号:</span>
                <span class="value">{{ detailData.invoice_detail.buyer_tax_id }}</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3 class="section-title">商品明细</h3>
            <a-table 
              :columns="invoiceColumns" 
              :data-source="detailData.invoice_detail.items" 
              :pagination="false"
              size="small"
              bordered
            />
          </div>

          <div class="detail-section">
            <h3 class="section-title">金额汇总</h3>
            <div class="amount-summary">
              <div class="summary-item">
                <span class="label">合计金额:</span>
                <span class="value amount">¥{{ formatNumber(detailData.invoice_detail.total_amount) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">合计税额:</span>
                <span class="value amount">¥{{ formatNumber(detailData.invoice_detail.total_tax) }}</span>
              </div>
              <div class="summary-item total">
                <span class="label">价税合计:</span>
                <span class="value amount">¥{{ formatNumber(detailData.invoice_detail.total_with_tax) }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 银行回单详情 -->
        <template v-else-if="voucher?.source_type === '银行回单' && detailData.bank_detail">
          <div class="detail-section">
            <h3 class="section-title">银行回单基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">银行名称:</span>
                <span class="value">{{ detailData.bank_detail.bank_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">账户名称:</span>
                <span class="value">{{ detailData.bank_detail.account_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">账户号码:</span>
                <span class="value">{{ detailData.bank_detail.account_number }}</span>
              </div>
              <div class="info-item">
                <span class="label">回单月份:</span>
                <span class="value">{{ detailData.bank_detail.months.join(', ') }}</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3 class="section-title">收入明细</h3>
            <a-table 
              :columns="bankIncomeColumns" 
              :data-source="detailData.bank_detail.income_transactions" 
              :pagination="false"
              size="small"
              bordered
            />
          </div>

          <div class="detail-section">
            <h3 class="section-title">支出明细</h3>
            <a-table 
              :columns="bankExpenseColumns" 
              :data-source="detailData.bank_detail.expense_transactions" 
              :pagination="false"
              size="small"
              bordered
            />
          </div>

          <div class="detail-section">
            <h3 class="section-title">汇总信息</h3>
            <div class="amount-summary">
              <div class="summary-item">
                <span class="label">收入总金额:</span>
                <span class="value amount income">¥{{ formatNumber(detailData.bank_detail.total_income) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">收入总笔数:</span>
                <span class="value">{{ detailData.bank_detail.income_count }}笔</span>
              </div>
              <div class="summary-item">
                <span class="label">支出总金额:</span>
                <span class="value amount expense">¥{{ formatNumber(detailData.bank_detail.total_expense) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">支出总笔数:</span>
                <span class="value">{{ detailData.bank_detail.expense_count }}笔</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 工资单详情 -->
        <template v-else-if="voucher?.source_type === '工资单' && detailData.payroll_detail">
          <div class="detail-section">
            <h3 class="section-title">工资单基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">公司名称:</span>
                <span class="value">{{ detailData.payroll_detail.company_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">工资月份:</span>
                <span class="value">{{ detailData.payroll_detail.months.join(', ') }}</span>
              </div>
              <div class="info-item">
                <span class="label">员工总数:</span>
                <span class="value">{{ detailData.payroll_detail.employee_count }}人</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3 class="section-title">员工工资明细</h3>
            <a-table 
              :columns="payrollColumns" 
              :data-source="detailData.payroll_detail.employees" 
              :pagination="{ pageSize: 10 }"
              size="small"
              bordered
            />
          </div>

          <div class="detail-section">
            <h3 class="section-title">汇总信息</h3>
            <div class="amount-summary">
              <div class="summary-item">
                <span class="label">税前工资总和:</span>
                <span class="value amount">¥{{ formatNumber(detailData.payroll_detail.total_gross_salary) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">个人社保公积金总和:</span>
                <span class="value amount">¥{{ formatNumber(detailData.payroll_detail.total_employee_deductions) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">公司社保公积金总和:</span>
                <span class="value amount">¥{{ formatNumber(detailData.payroll_detail.total_employer_contributions) }}</span>
              </div>
              <div class="summary-item total">
                <span class="label">实发工资总和:</span>
                <span class="value amount">¥{{ formatNumber(detailData.payroll_detail.total_net_salary) }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 无数据提示 -->
        <template v-else>
          <div class="no-data">
            <a-empty description="暂无详细数据" />
          </div>
        </template>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { Voucher } from '../types';

interface Props {
  voucher: Voucher | null;
  detailData: any;
  loading: boolean;
}

const props = defineProps<Props>();

// 格式化数字
function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

// 发票商品明细表格列
const invoiceColumns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '规格型号',
    dataIndex: 'specification',
    key: 'specification',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 60,
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 80,
    align: 'right',
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '税率',
    dataIndex: 'tax_rate',
    key: 'tax_rate',
    width: 80,
    align: 'right',
    customRender: ({ text }: any) => `${text}%`,
  },
  {
    title: '税额',
    dataIndex: 'tax_amount',
    key: 'tax_amount',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
];

// 银行收入明细表格列
const bankIncomeColumns = [
  {
    title: '交易日期',
    dataIndex: 'date',
    key: 'date',
    width: 100,
  },
  {
    title: '交易时间',
    dataIndex: 'time',
    key: 'time',
    width: 80,
  },
  {
    title: '对方账户',
    dataIndex: 'counterpart_account',
    key: 'counterpart_account',
    width: 150,
  },
  {
    title: '对方户名',
    dataIndex: 'counterpart_name',
    key: 'counterpart_name',
    width: 120,
  },
  {
    title: '收入金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '摘要',
    dataIndex: 'summary',
    key: 'summary',
    ellipsis: true,
  },
];

// 银行支出明细表格列
const bankExpenseColumns = [
  {
    title: '交易日期',
    dataIndex: 'date',
    key: 'date',
    width: 100,
  },
  {
    title: '交易时间',
    dataIndex: 'time',
    key: 'time',
    width: 80,
  },
  {
    title: '对方账户',
    dataIndex: 'counterpart_account',
    key: 'counterpart_account',
    width: 150,
  },
  {
    title: '对方户名',
    dataIndex: 'counterpart_name',
    key: 'counterpart_name',
    width: 120,
  },
  {
    title: '支出金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '摘要',
    dataIndex: 'summary',
    key: 'summary',
    ellipsis: true,
  },
];

// 工资单明细表格列
const payrollColumns = [
  {
    title: '员工姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 100,
  },
  {
    title: '职位',
    dataIndex: 'position',
    key: 'position',
    width: 100,
  },
  {
    title: '基本工资',
    dataIndex: 'basic_salary',
    key: 'basic_salary',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '绩效奖金',
    dataIndex: 'bonus',
    key: 'bonus',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '税前工资',
    dataIndex: 'gross_salary',
    key: 'gross_salary',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '个人社保',
    dataIndex: 'employee_social_insurance',
    key: 'employee_social_insurance',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '个人公积金',
    dataIndex: 'employee_housing_fund',
    key: 'employee_housing_fund',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '个人所得税',
    dataIndex: 'personal_income_tax',
    key: 'personal_income_tax',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
  {
    title: '实发工资',
    dataIndex: 'net_salary',
    key: 'net_salary',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => `¥${formatNumber(text)}`,
  },
];
</script>

<style scoped>
.source-data-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-content {
  padding: 16px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6f7ff;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.info-item .label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
}

.info-item .value {
  color: #333;
  font-weight: 600;
  text-align: right;
}

.amount-summary {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item.total {
  font-weight: 600;
  font-size: 16px;
  color: #1890ff;
  border-top: 2px solid #1890ff;
  margin-top: 8px;
  padding-top: 12px;
}

.summary-item .label {
  font-weight: 500;
  color: #666;
}

.summary-item .value {
  font-weight: 600;
  color: #333;
}

.summary-item .value.amount {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.summary-item .value.amount.income {
  color: #52c41a;
}

.summary-item .value.amount.expense {
  color: #ff4d4f;
}

.no-data {
  text-align: center;
  padding: 40px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 12px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  text-align: center;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #e6f7ff;
}

/* 滚动条样式 */
.source-data-detail::-webkit-scrollbar {
  width: 6px;
}

.source-data-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.source-data-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.source-data-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>

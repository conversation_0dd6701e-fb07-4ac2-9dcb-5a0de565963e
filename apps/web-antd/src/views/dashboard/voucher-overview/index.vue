<template>
  <div class="voucher-overview">
    <!-- 筛选器 -->
    <div class="filter-section mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="filter-checkboxes">
          <label class="checkbox-item">
            <input 
              type="checkbox" 
              value="invoice_in" 
              v-model="selectedFilters"
              @change="handleFilterChange"
            >
            <span class="checkbox-label">进项发票</span>
          </label>
          <label class="checkbox-item">
            <input 
              type="checkbox" 
              value="invoice_out" 
              v-model="selectedFilters"
              @change="handleFilterChange"
            >
            <span class="checkbox-label">销项发票</span>
          </label>
          <label class="checkbox-item">
            <input 
              type="checkbox" 
              value="bank_receipt" 
              v-model="selectedFilters"
              @change="handleFilterChange"
            >
            <span class="checkbox-label">银行回单</span>
          </label>
          <label class="checkbox-item">
            <input 
              type="checkbox" 
              value="payroll" 
              v-model="selectedFilters"
              @change="handleFilterChange"
            >
            <span class="checkbox-label">工资单</span>
          </label>
          <label class="checkbox-item">
            <input 
              type="checkbox" 
              value="reviewed" 
              v-model="selectedFilters"
              @change="handleFilterChange"
            >
            <span class="checkbox-label">已审核</span>
          </label>
          <label class="checkbox-item">
            <input 
              type="checkbox" 
              value="unreviewed" 
              v-model="selectedFilters"
              @change="handleFilterChange"
            >
            <span class="checkbox-label">未审核</span>
          </label>
        </div>
      </div>
    </div>

    <!-- 扑克牌式凭证卡片列表 -->
    <div class="poker-cards-container">
      <draggable 
        v-model="sortableVouchers" 
        group="vouchers"
        item-key="id"
        class="poker-cards-grid"
        :animation="200"
        ghost-class="ghost-card"
        chosen-class="chosen-card"
        drag-class="drag-card"
        @start="onDragStart"
        @end="onDragEnd"
      >
        <template #item="{ element: voucher }">
          <div class="poker-card-wrapper">
            <VoucherCard 
              :voucher="voucher" 
              class="poker-card"
              @save="handleSaveVoucher"
              @delete="handleDeleteVoucher"
              @view-details="handleViewDetails"
              @review-change="handleReviewChange"
            />
          </div>
        </template>
      </draggable>
    </div>

    <!-- 原始数据详情弹窗 -->
    <div v-if="detailModalVisible" class="custom-modal-overlay" @click="detailModalVisible = false">
      <div class="custom-modal" @click.stop>
        <div class="custom-modal-header">
          <h3>原始数据详情</h3>
          <button class="close-btn" @click="detailModalVisible = false">×</button>
        </div>
        <div class="custom-modal-body">
          <div v-if="detailLoading" style=" padding: 40px;text-align: center;">
            <div class="loading-spinner"></div>
            <div style="margin-top: 16px;">加载详情中...</div>
          </div>
          <div v-else-if="!detailData" style=" padding: 40px;text-align: center;">
            <div>暂无详细数据</div>
          </div>
          <SourceDataDetail 
            v-else
            :voucher="selectedVoucher" 
            :detail-data="detailData" 
            :loading="detailLoading" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { Checkbox, CheckboxGroup } from 'ant-design-vue';
import draggable from 'vuedraggable';
import VoucherCard from './components/VoucherCard.vue';
import SourceDataDetail from './components/SourceDataDetail.vue';
import { useWebSocket } from './composables/useWebSocket';
import type { Voucher } from './types';

// 响应式数据
const vouchers = ref<Voucher[]>([]);
const selectedFilters = ref<string[]>(['invoice_in', 'invoice_out', 'bank_receipt', 'payroll', 'reviewed', 'unreviewed']);
const detailModalVisible = ref(false);
const selectedVoucher = ref<Voucher | null>(null);
const detailData = ref<any>(null);
const detailLoading = ref(false);

// WebSocket连接
const { sendMessage, isConnected } = useWebSocket({
  onMessage: handleWebSocketMessage,
  onOpen: () => {
    console.log('凭证总览WebSocket连接成功');
    // 连接成功后立即请求凭证数据
    requestVouchers();
  },
  onClose: () => {
    console.log('凭证总览WebSocket连接断开');
  },
  onError: (error) => {
    console.error('WebSocket error:', error);
    message.error('WebSocket连接错误');
  },
});

// 请求凭证数据
function requestVouchers() {
  if (isConnected.value) {
    console.log('正在请求凭证数据...');
    sendMessage({
      type: 'get_vouchers',
    });
  } else {
    console.warn('WebSocket未连接，无法请求凭证数据');
  }
}

// 处理WebSocket消息
function handleWebSocketMessage(data: any) {
  switch (data.type) {
    case 'source_detail': {
      detailData.value = data.detail;
      detailLoading.value = false;
      break;
    }
    case 'voucher_added': {
      vouchers.value.push(data.voucher);
      break;
    }
    case 'voucher_deleted': {
      vouchers.value = vouchers.value.filter((v) => v.id !== data.id);
      break;
    }
    case 'voucher_updated': {
      updateVoucher(data.voucher);
      break;
    }
    case 'vouchers_list': {
      vouchers.value = data.vouchers;
      break;
    }
  }
}

// 更新凭证
function updateVoucher(updatedVoucher: Voucher) {
  const index = vouchers.value.findIndex((v) => v.id === updatedVoucher.id);
  if (index !== -1) {
    vouchers.value[index] = updatedVoucher;
  }
}

// 筛选后的凭证
const filteredVouchers = computed(() => {
  if (selectedFilters.value.length === 0) {
    return vouchers.value;
  }

  return vouchers.value.filter((voucher) => {
    // 按类型筛选
    const typeFilters = selectedFilters.value.filter((f) =>
      ['bank_receipt', 'invoice_in', 'invoice_out', 'payroll'].includes(f),
    );

    // 按审核状态筛选
    const reviewFilters = selectedFilters.value.filter((f) =>
      ['reviewed', 'unreviewed'].includes(f),
    );

    let typeMatch = true;
    let reviewMatch = true;

    if (typeFilters.length > 0) {
      const sourceTypeMap: Record<string, string> = {
        invoice_in: '进项发票',
        invoice_out: '销项发票',
        bank_receipt: '银行回单',
        payroll: '工资单',
      };

      typeMatch = typeFilters.some(
        (filter) => sourceTypeMap[filter] === voucher.source_type,
      );
    }

    if (reviewFilters.length > 0) {
      if (
        reviewFilters.includes('reviewed') &&
        reviewFilters.includes('unreviewed')
      ) {
        reviewMatch = true;
      } else if (reviewFilters.includes('reviewed')) {
        reviewMatch = voucher.reviewed;
      } else if (reviewFilters.includes('unreviewed')) {
        reviewMatch = !voucher.reviewed;
      }
    }

    return typeMatch && reviewMatch;
  });
});

// 可排序的凭证列表（用于拖拽）
const sortableVouchers = computed({
  get: () => filteredVouchers.value,
  set: (newOrder: Voucher[]) => {
    // 更新原始数据的顺序
    const newVouchers = [...vouchers.value];
    newOrder.forEach((voucher, index) => {
      const originalIndex = newVouchers.findIndex((v) => v.id === voucher.id);
      if (originalIndex !== -1) {
        newVouchers[originalIndex] = { ...voucher, sortOrder: index };
      }
    });
    vouchers.value = newVouchers;
  },
});

// 事件处理
function handleFilterChange() {
  // 筛选逻辑已在computed中处理
}

function handleSaveVoucher(voucher: Voucher) {
  if (isConnected.value) {
    sendMessage({
      type: 'update_voucher',
      voucher,
    });
  }
}

function handleDeleteVoucher(id: number) {
  if (isConnected.value) {
    sendMessage({
      type: 'delete_voucher',
      id,
    });
  }
}

function handleViewDetails(voucher: Voucher) {
  console.log('🔍 点击查看详情:', voucher.id, voucher.source_type);
  selectedVoucher.value = voucher;
  detailModalVisible.value = true;
  detailLoading.value = true;
  detailData.value = null;

  console.log('📋 弹窗状态:', {
    detailModalVisible: detailModalVisible.value,
    selectedVoucher: selectedVoucher.value?.id,
    detailLoading: detailLoading.value,
  });

  if (isConnected.value) {
    sendMessage({
      type: 'get_source_detail',
      voucher_id: voucher.id,
    });
  }
}

function handleReviewChange(voucher: Voucher) {
  handleSaveVoucher(voucher);
}

// 拖拽事件处理
function onDragStart(evt: any) {
  console.log('开始拖拽:', evt);
}

function onDragEnd(evt: any) {
  console.log('拖拽结束:', evt);
  // 可以在这里保存新的排序到后端
}

// 生命周期
onMounted(() => {
  // 请求获取所有凭证
  if (isConnected.value) {
    sendMessage({
      type: 'get_vouchers',
    });
  }
});
</script>



<style scoped>


/* 动画效果 */
@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }

  50% {
    transform: rotateY(90deg);
  }

  100% {
    transform: rotateY(0deg);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .poker-card-wrapper {
    width: 585px;
  }
}

@media (max-width: 768px) {
  .poker-cards-grid {
    justify-content: center;
  }

  .poker-card-wrapper {
    width: 100%;
    max-width: 320px;
  }
}

@media (max-width: 480px) {
  .poker-cards-container {
    padding: 10px 0;
  }

  .poker-cards-grid {
    gap: 12px;
  }
}

.voucher-overview {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.filter-section {
  padding: 16px;
  background: rgb(255 255 255 / 90%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(203 213 225 / 30%);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgb(0 0 0 / 5%);
}

.filter-section .ant-checkbox-wrapper {
  margin-right: 16px;
  margin-bottom: 8px;
}

.filter-section .ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  user-select: none;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.checkbox-item:hover {
  background-color: rgb(24 144 255 / 10%);
}

.checkbox-item input[type='checkbox'] {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  cursor: pointer;
}

.checkbox-label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

/* 扑克牌容器样式 */
.poker-cards-container {
  min-height: 400px;
  padding: 20px 0;
}

/* 扑克牌网格布局 */
.poker-cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  align-items: start;
}

/* 扑克牌包装器 */
.poker-card-wrapper {
  position: relative;
  width: 100%;
  min-height: 320px;
  padding: 8px;
  margin-bottom: 20px;
  cursor: grab;
  transition: all 0.3s ease;
}

.poker-card-wrapper::before {
  position: absolute;
  inset: 0;
  z-index: -1;
  content: '';
  background: linear-gradient(
    135deg,
    rgb(24 144 255 / 8%) 0%,
    rgb(139 92 246 / 8%) 100%
  );
  border-radius: 24px;
  box-shadow: 0 4px 20px rgb(24 144 255 / 15%);
}

.poker-card-wrapper::after {
  position: absolute;
  inset: -4px;
  z-index: -2;
  content: '';
  background: linear-gradient(
    135deg,
    rgb(24 144 255 / 15%) 0%,
    rgb(139 92 246 / 15%) 100%
  );
  border-radius: 28px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.poker-card-wrapper:hover::after {
  opacity: 1;
}

.poker-card-wrapper:hover {
  z-index: 10;
  transform: translateY(-8px) scale(1.02);
}

.poker-card-wrapper:active {
  cursor: grabbing;
}

/* 扑克牌样式 */
.poker-card {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #fff 0%, #fafbfc 50%, #f5f7fa 100%);
  backdrop-filter: blur(10px);
  border: 3px solid #1890ff;
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 12%),
    0 4px 16px rgb(0 0 0 / 8%),
    0 2px 8px rgb(24 144 255 / 15%),
    inset 0 1px 0 rgb(255 255 255 / 90%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(-2px);
}

.poker-card::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 5px;
  content: '';
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  border-radius: 16px 16px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.poker-card:hover::before {
  opacity: 1;
}

.poker-card:hover {
  background: linear-gradient(135deg, #fff 0%, #f1f5f9 50%, #ddd6fe 100%);
  border-color: #3b82f6;
  box-shadow: 0 12px 30px rgb(59 130 246 / 20%);
  transform: translateY(-2px);
}

/* 已审核卡牌样式 */
.poker-card:has(.voucher-card.reviewed) {
  border-color: #52c41a;
  box-shadow: 0 8px 25px rgb(82 196 26 / 15%);
}

.poker-card:has(.voucher-card.reviewed):hover {
  background: linear-gradient(135deg, #f6ffed 0%, #fff 50%, #f6ffed 100%);
  border-color: #73d13d;
  box-shadow: 0 12px 30px rgb(82 196 26 / 20%);
}

/* 拖拽状态样式 */
.ghost-card {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  opacity: 0.5;
  transform: rotate(5deg);
}

.chosen-card {
  z-index: 999;
  box-shadow: 0 10px 30px rgb(0 0 0 / 20%);
  transform: rotate(3deg) scale(1.05);
}

.drag-card {
  z-index: 1000;
  box-shadow: 0 15px 35px rgb(0 0 0 / 30%);
  opacity: 0.9;
  transform: rotate(-3deg) scale(1.1);
}

.poker-card.flip {
  animation: cardFlip 0.6s ease-in-out;
}

/* 自定义弹窗样式 */
.custom-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 60%);
  backdrop-filter: blur(4px);
}

.custom-modal {
  width: 90vw;
  max-width: 1000px;
  max-height: 80vh;
  overflow: hidden;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgb(0 0 0 / 30%);
  animation: modalSlideIn 0.3s ease-out;
}

.custom-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.custom-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 6px;
  transition: all 0.2s;
}

.close-btn:hover {
  color: #333;
  background: #f5f5f5;
}

.custom-modal-body {
  max-height: calc(80vh - 80px);
  padding: 24px;
  overflow-y: auto;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
</style>

// 凭证详情
export interface VoucherDetail {
  summary: string;
  account: string;
  debit: number;
  credit: number;
}

// 发票信息
export interface InvoiceInfo {
  fund_desc: string;
  amount: number;
  tax: number;
  total: number;
  id: number;
}

// 银行回单信息
export interface BankReceiptInfo {
  total_income_amount: number;
  income_transaction_num: number;
  total_expense_amount: number;
  expense_transaction_num: number;
  months: string[];
}

// 工资单信息
export interface PayrollInfo {
  total_gross_salary: number;
  total_employer_contributions: number;
  total_employee_deductions: number;
  months: string[];
}

// 原始数据信息
export interface SourceInfo {
  invoice_info?: InvoiceInfo;
  bank_receipt_info?: BankReceiptInfo;
  payroll_info?: PayrollInfo;
}

// 凭证类型
export interface Voucher {
  id: number;
  type: '记' | '借' | '转' | '结';
  record_date: string;
  details: VoucherDetail[];
  executor: 'people' | 'history' | 'llm';
  reviewed: boolean;
  source_type: '进项发票' | '销项发票' | '银行回单' | '工资单';
  source_info: SourceInfo;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// WebSocket配置
export interface WebSocketConfig {
  url?: string;
  onMessage: (data: any) => void;
  onError?: (error: Event) => void;
  onOpen?: () => void;
  onClose?: () => void;
}

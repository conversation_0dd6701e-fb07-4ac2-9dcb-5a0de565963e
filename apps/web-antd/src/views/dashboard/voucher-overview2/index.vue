<script setup lang="ts">
import type { Voucher } from './types';

import { computed, onMounted, ref } from 'vue';

import {
  DeleteOutlined,
  EditOutlined,
  MinusOutlined,
  MoreOutlined,
  PlusOutlined,
  RightOutlined,
  SaveOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';

import SourceDataDetail from './components/SourceDataDetail.vue';
import { useWebSocket } from './composables/useWebSocket';
// import { useWebSocket } from '@vueuse/core';

// 响应式数据
const vouchers = ref<Voucher[]>([]);
const searchText = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const jumpPage = ref(1);
const detailModalVisible = ref(false);
const selectedVoucher = ref<null | Voucher>(null);
const detailData = ref<any>(null);
const detailLoading = ref(false);

const { sendMessage, isConnected } = useWebSocket({
  onMessage: handleWebSocketMessage,
  onOpen: () => {
    console.log('凭证总览WebSocket连接成功');
    requestVouchers();
  },
  onClose: () => {
    console.log('凭证总览WebSocket连接断开');
  },
  onError: (error) => {
    console.error('WebSocket error:', error);
    message.error('WebSocket连接错误');
  },
});

// 计算属性
const totalVouchers = computed(() => vouchers.value.length);
const totalPages = computed(() =>
  Math.ceil(totalVouchers.value / pageSize.value),
);

const paginatedVouchers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return vouchers.value.slice(start, end);
});

// 请求凭证数据
function requestVouchers() {
  if (isConnected.value) {
    console.log('正在请求凭证数据...');
    sendMessage({
      type: 'get_vouchers',
    });
  } else {
    console.warn('WebSocket未连接，无法请求凭证数据');
  }
}

// 处理WebSocket消息
function handleWebSocketMessage(data: any) {
  switch (data.type) {
    case 'source_detail': {
      detailData.value = data.detail;
      detailLoading.value = false;
      break;
    }
    case 'voucher_added': {
      vouchers.value.push(data.voucher);
      break;
    }
    case 'voucher_deleted': {
      vouchers.value = vouchers.value.filter((v) => v.id !== data.id);
      break;
    }
    case 'voucher_updated': {
      updateVoucher(data.voucher);
      break;
    }
    case 'vouchers_list': {
      vouchers.value = data.vouchers;
      break;
    }
  }
}

// 更新凭证
function updateVoucher(updatedVoucher: Voucher) {
  const index = vouchers.value.findIndex((v) => v.id === updatedVoucher.id);
  if (index !== -1) {
    vouchers.value[index] = updatedVoucher;
  }
}

// 格式化数字
function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 计算借方总额
function getTotalDebit(voucher: Voucher): number {
  return voucher.details.reduce((sum, detail) => sum + (detail.debit || 0), 0);
}

// 计算贷方总额
function getTotalCredit(voucher: Voucher): number {
  return voucher.details.reduce((sum, detail) => sum + (detail.credit || 0), 0);
}

// 事件处理
function handleSearch() {
  // 搜索逻辑
}

function handleViewDetails(voucher: Voucher) {
  console.log('🔍 点击查看详情:', voucher.id, voucher.source_type);
  selectedVoucher.value = voucher;
  detailModalVisible.value = true;
  detailLoading.value = true;
  detailData.value = null;

  // if (isConnected.value) {
  //   sendMessage({
  //     type: 'get_source_detail',
  //     voucher_id: voucher.id,
  //   });
  // }
}

function handleDeleteDetail(voucherId: number, detailIndex: number) {
  // 删除明细逻辑
  console.log('删除明细:', voucherId, detailIndex);
}

function handleEditDetail(voucherId: number, detailIndex: number) {
  // 编辑明细逻辑
  console.log('编辑明细:', voucherId, detailIndex);
}

function handlePageChange(page: number) {
  if (page > 0 && page <= totalPages.value) {
    currentPage.value = page;
  }
}

function handlePageSizeChange(newPageSize: number) {
  pageSize.value = newPageSize;
  currentPage.value = 1;
}

function handleJumpToPage() {
  if (
    jumpPage.value &&
    jumpPage.value >= 1 &&
    jumpPage.value <= totalPages.value
  ) {
    currentPage.value = jumpPage.value;
  }
}

// 生命周期
onMounted(() => {
  if (isConnected.value) {
    sendMessage({
      type: 'get_vouchers',
    });
  }
});
</script>

<template>
  <div class="voucher-overview">
    <!-- 顶部操作栏 -->
    <div class="top-header">
      <!-- 左侧：搜索和更多查询 -->
      <div class="header-left">
        <div class="search-container">
          <a-input
            v-model:value="searchText"
            placeholder="请输入摘要"
            class="search-input"
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined class="search-icon" />
            </template>
          </a-input>
        </div>
        <a-button class="more-query-btn">
          更多查询1 <RightOutlined />
        </a-button>
      </div>

      <!-- 右侧：全局操作按钮 -->
      <div class="header-right">
        <a-button>新增凭证</a-button>
        <a-button>复制</a-button>
        <a-button>删除</a-button>
        <a-button>打印</a-button>
        <a-button>导出</a-button>
        <a-button> 更多 <MoreOutlined /> </a-button>
      </div>
    </div>

    <!-- 主表格 -->
    <div class="table-container">
      <table class="voucher-table">
        <thead>
          <tr>
            <th class="col-source">原始信息</th>
            <th class="col-summary">摘要</th>
            <th class="col-account">科目</th>
            <th class="col-debit">借方金额</th>
            <th class="col-credit">贷方金额</th>
            <th class="col-actions">操作</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="voucher in paginatedVouchers" :key="voucher.id">
            <!-- 凭证头部信息行 -->
            <tr class="voucher-header-row">
              <td colspan="6" class="voucher-header-cell">
                <div class="voucher-header-content">
                  <div class="voucher-info">
                    <a-checkbox class="voucher-checkbox" />
                    <span class="voucher-date">{{ voucher.record_date }}</span>
                    <span class="voucher-number"
                      >{{ voucher.type }} -
                      {{ String(voucher.id).padStart(3, '0') }}</span
                    >
                    <span v-if="voucher.executor === 'llm'" class="ai-badge"
                      >AI</span
                    >
                  </div>
                  <div class="voucher-actions">
                    <a-button size="small" type="text" class="action-btn">
                      <PlusOutlined /> 插入
                    </a-button>
                    <a-button size="small" type="text" class="action-btn">
                      <MinusOutlined /> 红冲
                    </a-button>
                    <a-button size="small" type="text" class="action-btn">
                      <SaveOutlined /> 保存
                    </a-button>
                  </div>
                </div>
              </td>
            </tr>

            <!-- 凭证明细行 -->
            <tr
              v-for="(detail, detailIndex) in voucher.details"
              :key="`${voucher.id}-${detailIndex}`"
              class="detail-row"
            >
              <!-- 原始信息列（只在第一行显示） -->
              <td
                v-if="detailIndex === 0"
                :rowspan="voucher.details.length + 1"
                class="source-info-cell"
              >
                <div class="source-info">
                  <div class="source-type">
                    <div class="source-title-section">
                      <span class="source-title">{{
                        voucher.source_type
                      }}</span>
                      <span
                        v-if="voucher.source_info.invoice_info"
                        class="source-invoice-number"
                        >[{{ voucher.source_info.invoice_info.id }}]</span
                      >
                    </div>
                    <a-button
                      type="link"
                      size="small"
                      @click="handleViewDetails(voucher)"
                      class="details-link"
                    >
                      详情
                    </a-button>
                  </div>
                  <div class="source-content">
                    <template v-if="voucher.source_info.invoice_info">
                      <div class="source-item">
                        {{ voucher.source_info.invoice_info.fund_desc }}
                      </div>
                      <div class="source-item">
                        金额/税: ¥{{
                          formatNumber(voucher.source_info.invoice_info.amount)
                        }}/¥{{
                          formatNumber(voucher.source_info.invoice_info.tax)
                        }}
                      </div>
                      <div class="source-item">
                        合计: ¥{{
                          formatNumber(voucher.source_info.invoice_info.total)
                        }}
                      </div>
                    </template>
                    <template v-else-if="voucher.source_info.bank_receipt_info">
                      <div class="source-item">
                        总收入: ¥{{
                          formatNumber(
                            voucher.source_info.bank_receipt_info
                              .total_income_amount,
                          )
                        }}
                        ({{
                          voucher.source_info.bank_receipt_info
                            .income_transaction_num
                        }}笔)
                      </div>
                      <div class="source-item">
                        总支出: ¥{{
                          formatNumber(
                            voucher.source_info.bank_receipt_info
                              .total_expense_amount,
                          )
                        }}
                        ({{
                          voucher.source_info.bank_receipt_info
                            .expense_transaction_num
                        }}笔)
                      </div>
                      <div class="source-item">
                        月份:
                        {{
                          voucher.source_info.bank_receipt_info.months.join(
                            ', ',
                          )
                        }}
                      </div>
                    </template>
                    <template v-else-if="voucher.source_info.payroll_info">
                      <div class="source-item">
                        总工资: ¥{{
                          formatNumber(
                            voucher.source_info.payroll_info.total_gross_salary,
                          )
                        }}
                      </div>
                      <div class="source-item">
                        雇主缴费: ¥{{
                          formatNumber(
                            voucher.source_info.payroll_info
                              .total_employer_contributions,
                          )
                        }}
                      </div>
                      <div class="source-item">
                        员工扣款: ¥{{
                          formatNumber(
                            voucher.source_info.payroll_info
                              .total_employee_deductions,
                          )
                        }}
                      </div>
                      <div class="source-item">
                        月份:
                        {{ voucher.source_info.payroll_info.months.join(', ') }}
                      </div>
                    </template>
                  </div>
                </div>
              </td>

              <!-- 摘要列 -->
              <td class="summary-cell">
                <div class="summary-text">{{ detail.summary }}</div>
              </td>

              <!-- 科目列 -->
              <td class="account-cell">
                <div class="account-text">{{ detail.account }}</div>
              </td>

              <!-- 借方金额列 -->
              <td class="debit-cell">
                <div class="amount-text debit" v-if="detail.debit > 0">
                  ¥{{ formatNumber(detail.debit) }}
                </div>
              </td>

              <!-- 贷方金额列 -->
              <td class="credit-cell">
                <div class="amount-text credit" v-if="detail.credit > 0">
                  ¥{{ formatNumber(detail.credit) }}
                </div>
              </td>

              <!-- 操作列 -->
              <td class="actions-cell">
                <div class="action-buttons">
                  <a-button
                    type="text"
                    size="small"
                    danger
                    class="delete-btn"
                    @click="handleDeleteDetail(voucher.id, detailIndex)"
                  >
                    <DeleteOutlined />
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    class="edit-btn"
                    @click="handleEditDetail(voucher.id, detailIndex)"
                  >
                    <EditOutlined />
                  </a-button>
                </div>
              </td>
            </tr>

            <!-- 合计行 -->
            <tr class="total-row">
              <td class="total-summary" colspan="2">
                <div class="total-text">合计</div>
              </td>
              <td class="total-debit">
                <div class="total-amount debit">
                  ¥{{ formatNumber(getTotalDebit(voucher)) }}
                </div>
              </td>
              <td class="total-credit">
                <div class="total-amount credit">
                  ¥{{ formatNumber(getTotalCredit(voucher)) }}
                </div>
              </td>
              <td class="total-actions"></td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <!-- 底部分页 -->
    <div class="bottom-footer">
      <span class="pagination-info">
        当前 第 {{ (currentPage - 1) * pageSize + 1 }} 到
        {{ Math.min(currentPage * pageSize, totalVouchers) }} 条 , 总共
        {{ totalVouchers }} 条 记录
      </span>
      <div class="pagination-controls">
        <a-select
          v-model:value="pageSize"
          @change="handlePageSizeChange"
          class="page-size-select"
        >
          <a-select-option value="10">10 条/页</a-select-option>
          <a-select-option value="20">20 条/页</a-select-option>
          <a-select-option value="50">50 条/页</a-select-option>
          <a-select-option value="100">100 条/页</a-select-option>
        </a-select>
        <a-button
          @click="handlePageChange(currentPage - 1)"
          :disabled="currentPage === 1"
          class="page-btn"
        >
          上一页
        </a-button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <a-button
          @click="handlePageChange(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="page-btn"
        >
          下一页
        </a-button>
        <span>到 第</span>
        <a-input-number
          v-model:value="jumpPage"
          :min="1"
          :max="totalPages"
          class="jump-input"
          @press-enter="handleJumpToPage"
        />
        <span>页</span>
        <a-button @click="handleJumpToPage" type="primary" class="confirm-btn">
          确定
        </a-button>
      </div>
    </div>

    <!-- 原始数据详情弹窗 -->
    <div
      v-if="detailModalVisible"
      class="custom-modal-overlay"
      @click="detailModalVisible = false"
    >
      <div class="custom-modal" @click.stop>
        <div class="custom-modal-header">
          <h3>原始数据详情</h3>
          <button class="close-btn" @click="detailModalVisible = false">
            ×
          </button>
        </div>
        <div class="custom-modal-body">
          <div v-if="detailLoading" style="padding: 40px; text-align: center">
            <div class="loading-spinner"></div>
            <div style="margin-top: 16px">加载详情中...</div>
          </div>
          <div
            v-else-if="!detailData"
            style="padding: 40px; text-align: center"
          >
            <div>暂无详细数据</div>
          </div>
          <SourceDataDetail
            v-else
            :voucher="selectedVoucher"
            :detail-data="detailData"
            :loading="detailLoading"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }

  .bottom-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
  }

  .table-container {
    overflow-x: auto;
  }

  .voucher-table {
    min-width: 800px;
  }
}

.voucher-overview {
  min-height: 100vh;
  padding: 16px;
  background: #f5f5f5;
}

/* 顶部操作栏 */
.top-header {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
}

.header-left {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-container {
  position: relative;
}

.search-input {
  width: 200px;
}

.search-icon {
  color: #999;
}

.more-query-btn {
  display: flex;
  gap: 4px;
  align-items: center;
}

.header-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 表格容器 */
.table-container {
  overflow-x: auto;
  background: white;
  border: 1px solid #e8e8e8;
  border-top: none;
}

.voucher-table {
  width: 100%;
  font-size: 14px;
  border-collapse: collapse;
}

.voucher-table thead {
  background: #fafafa;
}

.voucher-table th {
  padding: 12px 16px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  text-align: left;
  text-transform: uppercase;
  border-bottom: 1px solid #e8e8e8;
}

.col-source {
  width: 26%;
}

.col-summary {
  width: 22%;
}

.col-account {
  width: 18%;
}

.col-debit {
  width: 12%;
  text-align: right;
}

.col-credit {
  width: 12%;
  text-align: right;
}

.col-actions {
  width: 10%;
  text-align: center;
}

/* 凭证头部行 */
.voucher-header-row {
  background: #f0f0f0;
}

.voucher-header-cell {
  padding: 8px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.voucher-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.voucher-info {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.voucher-checkbox {
  margin-right: 8px;
}

.voucher-date {
  font-size: 12px;
  color: #666;
}

.voucher-number {
  font-size: 12px;
  color: #666;
}

.ai-badge {
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  color: #856404;
  background: #fff3cd;
  border-radius: 4px;
}

.voucher-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.action-btn {
  display: flex;
  gap: 4px;
  align-items: center;
  height: auto;
  padding: 4px 8px;
  font-size: 12px;
}

/* 明细行 */
.detail-row {
  border-bottom: 1px solid #f0f0f0;
}

.detail-row:hover {
  background: #fafafa;
}

.voucher-table td {
  padding: 8px 16px;
  vertical-align: top;
}

/* 原始信息列 */
.source-info-cell {
  border-right: 1px solid #e8e8e8;
}

.source-info {
  font-size: 12px;
}

.source-type {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1890ff;
}

.source-title-section {
  display: flex;
  gap: 6px;
  align-items: center;
}

.source-title {
  font-size: 12px;
  font-weight: 600;
}

.source-invoice-number {
  font-size: 11px;
  font-weight: 400;
  color: #666;
}

.details-link {
  height: auto;
  padding: 0;
  font-size: 12px;
}

.source-content {
  color: #666;
}

.source-item {
  margin-bottom: 4px;
  line-height: 1.4;
}

/* 摘要和科目列 */
.summary-cell,
.account-cell {
  font-size: 12px;
}

.summary-text,
.account-text {
  line-height: 1.4;
  color: #333;
}

/* 金额列 */
.debit-cell,
.credit-cell {
  text-align: right;
}

.amount-text {
  font-size: 12px;
  font-weight: 600;
}

.amount-text.debit {
  color: #52c41a;
}

.amount-text.credit {
  color: #f5222d;
}

/* 操作列 */
.actions-cell {
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.delete-btn,
.edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  padding: 4px;
}

/* 合计行 */
.total-row {
  font-weight: 600;
  background: #e6f7ff;
  border-top: 2px solid #1890ff;
}

.total-summary {
  font-size: 12px;
}

.total-text {
  color: #333;
}

.total-debit,
.total-credit {
  text-align: right;
}

.total-amount {
  font-size: 12px;
  font-weight: 600;
}

.total-amount.debit {
  color: #389e0d;
}

.total-amount.credit {
  color: #cf1322;
}

/* 底部分页 */
.bottom-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  font-size: 14px;
  color: #666;
  background: white;
  border: 1px solid #e8e8e8;
  border-top: none;
  border-radius: 0 0 8px 8px;
}

.pagination-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.page-size-select {
  width: 240px;
}

.page-btn {
  min-width: 64px;
}

.page-info {
  padding: 0 8px;
}

.jump-input {
  width: 60px;
}

.confirm-btn {
  min-width: 48px;
}

/* 自定义弹窗样式 */
.custom-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 60%);
  backdrop-filter: blur(4px);
}

.custom-modal {
  width: 90vw;
  max-width: 1000px;
  max-height: 80vh;
  overflow: hidden;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgb(0 0 0 / 30%);
  animation: modalSlideIn 0.3s ease-out;
}

.custom-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.custom-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 6px;
  transition: all 0.2s;
}

.close-btn:hover {
  color: #333;
  background: #f5f5f5;
}

.custom-modal-body {
  max-height: calc(80vh - 80px);
  padding: 24px;
  overflow-y: auto;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
</style>

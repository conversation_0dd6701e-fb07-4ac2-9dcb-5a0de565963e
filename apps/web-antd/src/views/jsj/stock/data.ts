import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

// import { getDictOptions } from '#/utils/dict'; // 已删除dict工具
import { renderDict } from '#/utils/render';

// 临时空函数，字典功能已移除
const getDictOptions = () => [];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.STOCK_TYPE 便于维护
      options: getDictOptions('stock_type'),
    },
    fieldName: 'type',
    label: '类型',
  },
  {
    component: 'Input',
    fieldName: 'no',
    label: '单号',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'stockDate',
    label: '日期',
  },
  {
    component: 'Input',
    fieldName: 'num',
    label: '数量',
  },
  {
    component: 'Input',
    fieldName: 'totalMoneyAmount',
    label: '价税合计',
  },
  {
    component: 'Input',
    fieldName: 'taxMoneyAmount',
    label: '税额',
  },
  {
    component: 'Input',
    fieldName: 'notTaxMoney',
    label: '金额',
  },
  {
    component: 'Input',
    fieldName: 'isAccount',
    label: '是否已经记账',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.STOCK_FONT_TYPE 便于维护
      options: getDictOptions('stock_font_type'),
    },
    fieldName: 'stockFontType',
    label: '蓝字/红字',
  },
  {
    component: 'Input',
    fieldName: 'voucherId',
    label: '凭证ID',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'accountDate',
    label: '记账日期',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '',
    field: 'id',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '类型',
    field: 'type',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.STOCK_TYPE 便于维护
        return renderDict(row.type, 'stock_type');
      },
    },
  },
  {
    title: '单号',
    field: 'no',
  },
  {
    title: '日期',
    field: 'stockDate',
  },
  {
    title: '数量',
    field: 'num',
  },
  {
    title: '价税合计',
    field: 'totalMoneyAmount',
  },
  {
    title: '税额',
    field: 'taxMoneyAmount',
  },
  {
    title: '金额',
    field: 'notTaxMoney',
  },
  {
    title: '是否已经记账',
    field: 'isAccount',
  },
  {
    title: '蓝字/红字',
    field: 'stockFontType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.STOCK_FONT_TYPE 便于维护
        return renderDict(row.stockFontType, 'stock_font_type');
      },
    },
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    title: '凭证ID',
    field: 'voucherId',
  },
  {
    title: '记账日期',
    field: 'accountDate',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '类型',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.STOCK_TYPE 便于维护
      options: getDictOptions('stock_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '单号',
    fieldName: 'no',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '日期',
    fieldName: 'stockDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    rules: 'required',
  },
  {
    label: '数量',
    fieldName: 'num',
    component: 'Input',
  },
  {
    label: '价税合计',
    fieldName: 'totalMoneyAmount',
    component: 'Input',
  },
  {
    label: '税额',
    fieldName: 'taxMoneyAmount',
    component: 'Input',
  },
  {
    label: '金额',
    fieldName: 'notTaxMoney',
    component: 'Input',
  },
  {
    label: '是否已经记账',
    fieldName: 'isAccount',
    component: 'Input',
  },
  {
    label: '蓝字/红字',
    fieldName: 'stockFontType',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.STOCK_FONT_TYPE 便于维护
      options: getDictOptions('stock_font_type'),
    },
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Input',
  },
  {
    label: '凭证ID',
    fieldName: 'voucherId',
    component: 'Input',
  },
  {
    label: '记账日期',
    fieldName: 'accountDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

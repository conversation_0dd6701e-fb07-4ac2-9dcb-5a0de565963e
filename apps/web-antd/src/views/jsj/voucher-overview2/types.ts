// 凭证详情
export interface VoucherDetail {
  summary: string;
  account: string;
  debit: number;
  credit: number;
}

// 原始文件信息
export interface OriginalFile {
  url: string;
  desc: string;
}

// 发票信息
export interface InvoiceInfo {
  fund_desc: string;
  amount: number;
  tax: number;
  total: number;
  id: number;
  orign_files?: OriginalFile[];
}

// 单个银行回单记录
export interface BankReceiptRecord {
  id: string;
  transaction_id: string;
  transaction_time: string;
  account_number: string;
  account_name: string;
  bank_name: string | null;
  conterpary_account_number: string;
  conterpary_account_name: string;
  conterpary_bank_name: string;
  type: string;
  amount: number;
  currency: string;
  summary: string;
  note: string | null;
}

// 银行回单信息（支持对象和数组格式）
export interface BankReceiptInfo {
  total_income_amount: number;
  income_transaction_num: number;
  total_expense_amount: number;
  expense_transaction_num: number;
  months: string[];
  timestamp?: string;
  orign_files?: OriginalFile[];
}

// 银行回单信息类型（兼容合并格式）
export type BankReceiptInfoType = BankReceiptInfo | BankReceiptRecord[];

// 工资单信息
export interface PayrollInfo {
  total_gross_salary: number;
  total_employer_contributions: number;
  total_employee_deductions: number;
  months: string[];
  timestamp?: string;
}

// 原始数据信息
export interface SourceInfo {
  invoice_info?: InvoiceInfo;
  bank_receipt_info?: BankReceiptInfoType;
  payroll_info?: PayrollInfo;
  input_invoice?: InvoiceInfo[];
  output_invoice?: InvoiceInfo[];
}

// 凭证类型
export interface Voucher {
  id: number | string;
  type: '借' | '结' | '记' | '转';
  record_date: string;
  details: VoucherDetail[];
  executor: 'ai' | 'history' | 'llm' | 'people';
  reviewed: boolean;
  confirmed?: boolean;
  source_type: '工资单' | '进项发票' | '银行回单' | '销项发票';
  source_info: SourceInfo;
  is_generated?: boolean; // 是否为AI生成的凭证
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// WebSocket配置
export interface WebSocketConfig {
  url?: string;
  onMessage: (data: any) => void;
  onError?: (error: Event) => void;
  onOpen?: () => void;
  onClose?: () => void;
}

// 合并凭证信息
export interface MergeVoucherInfo {
  selectedVouchers: Voucher[];
  totalAmount: number;
  totalTransactions: number;
}

<script setup lang="ts">
  import { ref } from 'vue';

  import { DownloadOutlined, EyeOutlined } from '@ant-design/icons-vue';

  interface Props {
    detailData: any;
    loading: boolean;
  }

  defineProps<Props>();

  // 文件预览相关状态
  const previewVisible = ref(false);
  const previewUrl = ref('');
  const previewTitle = ref('');

  // 格式化数字
  function formatNumber(num: number): string {
    return num.toLocaleString('zh-CN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  }

  // 预览文件
  function previewFile(url: string, title: string = '') {
    previewUrl.value = url;
    previewTitle.value = title || '文件预览';
    previewVisible.value = true;
  }

  // 下载文件
  function downloadFile(url: string, filename: string = '') {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || url.split('/').pop() || 'download';
    link.target = '_blank';
    document.body.append(link);
    link.click();
    link.remove();
  }

  // 关闭预览
  function closePreview() {
    previewVisible.value = false;
    previewUrl.value = '';
    previewTitle.value = '';
  }

  // 获取文件名
  function getFileName(url: string): string {
    return url.split('/').pop() || '未知文件';
  }

  // 判断文件类型
  function getFileType(url: string): string {
    const ext = url.split('.').pop()?.toLowerCase() || '';
    if (['gif', 'jpeg', 'jpg', 'png', 'webp'].includes(ext)) {
      return 'image';
    } else if (ext === 'pdf') {
      return 'pdf';
    } else {
      return 'other';
    }
  }
</script>

<template>
  <div class="source-data-detail">
    <a-spin :spinning="loading" tip="加载详情中...">
      <div v-if="!detailData" class="no-data">
        <a-empty description="暂无数据" />
      </div>

      <div v-else class="data-content">
        <!-- 进项发票数据 -->
        <div
          v-if="detailData.input_invoice && detailData.input_invoice.length > 0"
          class="data-section"
        >
          <h3 class="section-title">进项发票</h3>
          <div class="invoice-list">
            <div
              v-for="(invoice, index) in detailData.input_invoice"
              :key="index"
              class="invoice-item"
            >
              <a-descriptions :column="2" bordered size="small">
                <a-descriptions-item label="发票代码">
                  {{ invoice.invoice_code }}
                </a-descriptions-item>
                <a-descriptions-item label="发票号码">
                  {{ invoice.invoice_number }}
                </a-descriptions-item>
                <a-descriptions-item label="销售方名称">
                  {{ invoice.seller_name }}
                </a-descriptions-item>
                <a-descriptions-item label="购买方名称">
                  {{ invoice.buyer_name }}
                </a-descriptions-item>
                <a-descriptions-item label="开票日期">
                  {{ invoice.issue_date }}
                </a-descriptions-item>
                <a-descriptions-item label="货物名称">
                  {{ invoice.goods_name }}
                </a-descriptions-item>
                <a-descriptions-item label="规格型号">
                  {{ invoice.specification }}
                </a-descriptions-item>
                <a-descriptions-item label="单位">
                  {{ invoice.unit }}
                </a-descriptions-item>
                <a-descriptions-item label="数量">
                  {{ invoice.quantity }}
                </a-descriptions-item>
                <a-descriptions-item label="单价">
                  ¥{{ formatNumber(invoice.unit_price) }}
                </a-descriptions-item>
                <a-descriptions-item label="金额">
                  ¥{{ formatNumber(invoice.amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="税率">
                  {{ (invoice.tax_rate * 100).toFixed(2) }}%
                </a-descriptions-item>
                <a-descriptions-item label="税额">
                  ¥{{ formatNumber(invoice.tax_amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="价税合计">
                  ¥{{ formatNumber(invoice.total_amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="发票状态">
                  {{ invoice.status }}
                </a-descriptions-item>
                <a-descriptions-item label="风险等级">
                  {{ invoice.risk_level }}
                </a-descriptions-item>
                <a-descriptions-item label="开票人">
                  {{ invoice.issuer }}
                </a-descriptions-item>
                <a-descriptions-item label="备注" :span="2">
                  {{ invoice.description }}
                </a-descriptions-item>
              </a-descriptions>

              <!-- 进项发票原始文件 -->
              <div
                v-if="invoice.orign_files && invoice.orign_files.length > 0"
                class="invoice-files"
              >
                <h4 class="files-title">原始文件</h4>
                <div class="files-grid">
                  <div
                    v-for="(file, fileIndex) in invoice.orign_files"
                    :key="fileIndex"
                    class="file-item"
                  >
                    <div class="file-info">
                      <div class="file-name" :title="getFileName(file.url)">
                        {{ getFileName(file.url) }}
                      </div>
                      <div v-if="file.desc" class="file-desc">
                        {{ file.desc }}
                      </div>
                    </div>
                    <div class="file-actions">
                      <a-button
                        type="text"
                        size="small"
                        @click="previewFile(file.url, getFileName(file.url))"
                      >
                        <template #icon>
                          <EyeOutlined />
                        </template>
                        预览
                      </a-button>
                      <a-button
                        type="text"
                        size="small"
                        @click="downloadFile(file.url, getFileName(file.url))"
                      >
                        <template #icon>
                          <DownloadOutlined />
                        </template>
                        下载
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 销项发票数据 -->
        <div
          v-if="
            detailData.output_invoice && detailData.output_invoice.length > 0
          "
          class="data-section"
        >
          <h3 class="section-title">销项发票</h3>
          <div class="invoice-list">
            <div
              v-for="(invoice, index) in detailData.output_invoice"
              :key="index"
              class="invoice-item"
            >
              <a-descriptions :column="2" bordered size="small">
                <a-descriptions-item label="发票代码">
                  {{ invoice.invoice_code }}
                </a-descriptions-item>
                <a-descriptions-item label="发票号码">
                  {{ invoice.invoice_number }}
                </a-descriptions-item>
                <a-descriptions-item label="销售方名称">
                  {{ invoice.seller_name }}
                </a-descriptions-item>
                <a-descriptions-item label="购买方名称">
                  {{ invoice.buyer_name }}
                </a-descriptions-item>
                <a-descriptions-item label="开票日期">
                  {{ invoice.issue_date }}
                </a-descriptions-item>
                <a-descriptions-item label="货物名称">
                  {{ invoice.goods_name }}
                </a-descriptions-item>
                <a-descriptions-item label="规格型号">
                  {{ invoice.specification }}
                </a-descriptions-item>
                <a-descriptions-item label="单位">
                  {{ invoice.unit }}
                </a-descriptions-item>
                <a-descriptions-item label="数量">
                  {{ invoice.quantity }}
                </a-descriptions-item>
                <a-descriptions-item label="单价">
                  ¥{{ formatNumber(invoice.unit_price) }}
                </a-descriptions-item>
                <a-descriptions-item label="金额">
                  ¥{{ formatNumber(invoice.amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="税率">
                  {{ (invoice.tax_rate * 100).toFixed(2) }}%
                </a-descriptions-item>
                <a-descriptions-item label="税额">
                  ¥{{ formatNumber(invoice.tax_amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="价税合计">
                  ¥{{ formatNumber(invoice.total_amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="发票状态">
                  {{ invoice.status }}
                </a-descriptions-item>
                <a-descriptions-item label="风险等级">
                  {{ invoice.risk_level }}
                </a-descriptions-item>
                <a-descriptions-item label="开票人">
                  {{ invoice.issuer }}
                </a-descriptions-item>
                <a-descriptions-item label="备注" :span="2">
                  {{ invoice.description }}
                </a-descriptions-item>
              </a-descriptions>

              <!-- 销项发票原始文件 -->
              <div
                v-if="invoice.orign_files && invoice.orign_files.length > 0"
                class="invoice-files"
              >
                <h4 class="files-title">原始文件</h4>
                <div class="files-grid">
                  <div
                    v-for="(file, fileIndex) in invoice.orign_files"
                    :key="fileIndex"
                    class="file-item"
                  >
                    <div class="file-info">
                      <div class="file-name" :title="getFileName(file.url)">
                        {{ getFileName(file.url) }}
                      </div>
                      <div v-if="file.desc" class="file-desc">
                        {{ file.desc }}
                      </div>
                    </div>
                    <div class="file-actions">
                      <a-button
                        type="text"
                        size="small"
                        @click="previewFile(file.url, getFileName(file.url))"
                      >
                        <template #icon>
                          <EyeOutlined />
                        </template>
                        预览
                      </a-button>
                      <a-button
                        type="text"
                        size="small"
                        @click="downloadFile(file.url, getFileName(file.url))"
                      >
                        <template #icon>
                          <DownloadOutlined />
                        </template>
                        下载
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 银行回单数据 -->
        <div
          v-if="detailData.bank_receipt && detailData.bank_receipt.length > 0"
          class="data-section"
        >
          <h3 class="section-title">银行回单</h3>
          <div class="bank-receipt-list">
            <div
              v-for="(receipt, index) in detailData.bank_receipt"
              :key="index"
              class="receipt-item"
            >
              <a-descriptions :column="2" bordered size="small">
                <a-descriptions-item label="交易流水号">
                  {{ receipt.transaction_id }}
                </a-descriptions-item>
                <a-descriptions-item label="交易时间">
                  {{ receipt.transaction_time }}
                </a-descriptions-item>
                <a-descriptions-item label="账户号码">
                  {{ receipt.account_number }}
                </a-descriptions-item>
                <a-descriptions-item label="账户名称">
                  {{ receipt.account_name }}
                </a-descriptions-item>
                <a-descriptions-item label="开户银行">
                  {{ receipt.bank_name }}
                </a-descriptions-item>
                <a-descriptions-item label="对方账户号码">
                  {{ receipt.conterpary_account_number }}
                </a-descriptions-item>
                <a-descriptions-item label="对方账户名称">
                  {{ receipt.conterpary_account_name }}
                </a-descriptions-item>
                <a-descriptions-item label="对方开户银行">
                  {{ receipt.conterpary_bank_name }}
                </a-descriptions-item>
                <a-descriptions-item label="交易类型">
                  {{ receipt.type }}
                </a-descriptions-item>
                <a-descriptions-item label="交易金额">
                  ¥{{ formatNumber(receipt.amount) }}
                </a-descriptions-item>
                <a-descriptions-item label="币种">
                  {{ receipt.currency }}
                </a-descriptions-item>
                <a-descriptions-item label="摘要">
                  {{ receipt.summary }}
                </a-descriptions-item>
                <a-descriptions-item label="备注" :span="2">
                  {{ receipt.note }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </div>

        <!-- 银行回单原始文件 -->
        <div
          v-if="
            detailData.bank_receipt_info &&
            detailData.bank_receipt_info.orign_files &&
            detailData.bank_receipt_info.orign_files.length > 0
          "
          class="data-section"
        >
          <h3 class="section-title">银行回单原始文件</h3>
          <div class="original-files">
            <div class="files-grid">
              <div
                v-for="(file, index) in detailData.bank_receipt_info
                  .orign_files"
                :key="index"
                class="file-item"
              >
                <div class="file-info">
                  <div class="file-name" :title="getFileName(file.url)">
                    {{ getFileName(file.url) }}
                  </div>
                  <div v-if="file.desc" class="file-desc">
                    {{ file.desc }}
                  </div>
                </div>
                <div class="file-actions">
                  <a-button
                    type="text"
                    size="small"
                    @click="previewFile(file.url, getFileName(file.url))"
                  >
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    预览
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    @click="downloadFile(file.url, getFileName(file.url))"
                  >
                    <template #icon>
                      <DownloadOutlined />
                    </template>
                    下载
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 工资单数据 -->
        <div
          v-if="detailData.payroll_info && detailData.payroll_info.length > 0"
          class="data-section"
        >
          <h3 class="section-title">工资单</h3>
          <div class="payroll-list">
            <div
              v-for="(payroll, index) in detailData.payroll_info"
              :key="index"
              class="payroll-item"
            >
              <a-descriptions :column="2" bordered size="small">
                <a-descriptions-item label="员工编号">
                  {{ payroll.employee_id }}
                </a-descriptions-item>
                <a-descriptions-item label="姓名">
                  {{ payroll.name }}
                </a-descriptions-item>
                <a-descriptions-item label="身份证号">
                  {{ payroll.id_number }}
                </a-descriptions-item>
                <a-descriptions-item label="总工资">
                  ¥{{ formatNumber(payroll.total_salary) }}
                </a-descriptions-item>
                <a-descriptions-item label="基本工资">
                  ¥{{ formatNumber(payroll.base_salary) }}
                </a-descriptions-item>
                <a-descriptions-item label="绩效工资">
                  ¥{{ formatNumber(payroll.performance_salary) }}
                </a-descriptions-item>
                <a-descriptions-item label="岗位工资">
                  ¥{{ formatNumber(payroll.position_salary) }}
                </a-descriptions-item>
                <a-descriptions-item label="应税工资">
                  ¥{{ formatNumber(payroll.taxable_salary) }}
                </a-descriptions-item>
                <a-descriptions-item label="个人养老保险">
                  ¥{{ formatNumber(payroll.pension_personal) }}
                </a-descriptions-item>
                <a-descriptions-item label="个人医疗保险">
                  ¥{{ formatNumber(payroll.medical_personal) }}
                </a-descriptions-item>
                <a-descriptions-item label="个人失业保险">
                  ¥{{ formatNumber(payroll.unemployment_personal) }}
                </a-descriptions-item>
                <a-descriptions-item label="个人住房公积金">
                  ¥{{ formatNumber(payroll.housing_fund_personal) }}
                </a-descriptions-item>
                <a-descriptions-item label="公司养老保险">
                  ¥{{ formatNumber(payroll.pension_company) }}
                </a-descriptions-item>
                <a-descriptions-item label="公司医疗保险">
                  ¥{{ formatNumber(payroll.medical_company) }}
                </a-descriptions-item>
                <a-descriptions-item label="公司失业保险">
                  ¥{{ formatNumber(payroll.unemployment_company) }}
                </a-descriptions-item>
                <a-descriptions-item label="公司住房公积金">
                  ¥{{ formatNumber(payroll.housing_fund_company) }}
                </a-descriptions-item>
                <a-descriptions-item label="补充住房公积金">
                  ¥{{ formatNumber(payroll.extra_housing_fund) }}
                </a-descriptions-item>
                <a-descriptions-item label="补充医疗保险">
                  ¥{{ formatNumber(payroll.extra_medical) }}
                </a-descriptions-item>
                <a-descriptions-item label="实发工资">
                  ¥{{ formatNumber(payroll.actual_salary) }}
                </a-descriptions-item>
                <a-descriptions-item label="备注" :span="2">
                  {{ payroll.remark }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </div>
      </div>
    </a-spin>

    <!-- 文件预览模态框 -->
    <a-modal
      v-model:open="previewVisible"
      :title="previewTitle"
      width="80%"
      :footer="null"
      :centered="true"
      @cancel="closePreview"
    >
      <div class="file-preview-container">
        <!-- PDF 预览 -->
        <iframe
          v-if="getFileType(previewUrl) === 'pdf'"
          :src="previewUrl"
          class="pdf-preview"
          frameborder="0"
        ></iframe>

        <!-- 图片预览 -->
        <img
          v-else-if="getFileType(previewUrl) === 'image'"
          :src="previewUrl"
          :alt="previewTitle"
          class="image-preview"
        />

        <!-- 其他文件类型 -->
        <div v-else class="other-file-preview">
          <div class="file-icon">📄</div>
          <div class="file-info">
            <p>{{ previewTitle }}</p>
            <p class="file-tip">此文件类型不支持在线预览，请下载后查看</p>
            <a-button
              type="primary"
              @click="downloadFile(previewUrl, previewTitle)"
            >
              <template #icon>
                <DownloadOutlined />
              </template>
              下载文件
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 768px) {
    .files-grid {
      grid-template-columns: 1fr;
    }

    .file-item {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }

    .file-actions {
      justify-content: flex-end;
      width: 100%;
    }
  }

  .source-data-detail {
    padding: 16px;
  }

  .no-data {
    padding: 40px;
    text-align: center;
  }

  .data-content {
    max-height: 600px;
    overflow-y: auto;
  }

  .data-section {
    margin-bottom: 24px;
  }

  .section-title {
    padding-bottom: 4px;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
    border-bottom: 2px solid #1890ff;
  }

  .invoice-item,
  .receipt-item,
  .payroll-item {
    padding: 12px;
    margin-bottom: 16px;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  .invoice-item:last-child,
  .receipt-item:last-child,
  .payroll-item:last-child {
    margin-bottom: 0;
  }

  /* 原始文件样式 */
  .original-files {
    padding: 12px;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  .files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
  }

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .file-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 4px rgb(24 144 255 / 10%);
  }

  .file-info {
    flex: 1;
    min-width: 0;
  }

  .file-name {
    margin-bottom: 4px;
    overflow: hidden;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .file-desc {
    overflow: hidden;
    font-size: 12px;
    color: #666;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .file-actions {
    display: flex;
    flex-shrink: 0;
    gap: 4px;
  }

  /* 文件预览样式 */
  .file-preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 70vh;
  }

  .pdf-preview {
    width: 100%;
    height: 100%;
    border: none;
  }

  .image-preview {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .other-file-preview {
    padding: 40px;
    text-align: center;
  }

  .file-icon {
    margin-bottom: 16px;
    font-size: 64px;
  }

  .file-info p {
    margin-bottom: 8px;
  }

  .file-tip {
    margin-bottom: 16px !important;
    font-size: 14px;
    color: #666;
  }

  /* 发票文件样式 */
  .invoice-files {
    padding: 8px;
    margin-top: 12px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
  }

  .files-title {
    margin: 0 0 8px;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
  }
</style>

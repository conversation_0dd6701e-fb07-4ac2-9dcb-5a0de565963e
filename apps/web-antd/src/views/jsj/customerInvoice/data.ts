import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

// import { getDictOptions } from '#/utils/dict'; // 已删除dict工具
import { renderDict } from '#/utils/render';

// 临时空函数，字典功能已移除
const getDictOptions = () => [];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'oldInvoiceId',
    label: '老jsj发票id',
  },
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'invoiceCode',
    label: '发票代码',
  },
  {
    component: 'Input',
    fieldName: 'invoiceNumber',
    label: '发票号码',
  },
  {
    component: 'Input',
    fieldName: 'digitalInvoiceNumber',
    label: '数电票号码',
  },
  {
    component: 'Input',
    fieldName: 'sellerTaxId',
    label: '销方识别号',
  },
  {
    component: 'Input',
    fieldName: 'sellerName',
    label: '销方名称',
  },
  {
    component: 'Input',
    fieldName: 'buyerTaxId',
    label: '购方识别号',
  },
  {
    component: 'Input',
    fieldName: 'buyerName',
    label: '购方名称',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'invoiceDate',
    label: '开票日期',
  },
  {
    component: 'Input',
    fieldName: 'amount',
    label: '金额',
  },
  {
    component: 'Input',
    fieldName: 'taxAmount',
    label: '税额',
  },
  {
    component: 'Input',
    fieldName: 'totalAmount',
    label: '价税合计',
  },
  {
    component: 'Input',
    fieldName: 'invoiceSource',
    label: '发票来源',
  },
  {
    component: 'Input',
    fieldName: 'invoiceType',
    label: '发票票种',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_ONE_ZERO 便于维护
      options: getDictOptions('sys_one_zero'),
    },
    fieldName: 'isPositiveInvoice',
    label: '是否正数发票',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_RISK_LEVEL 便于维护
      options: getDictOptions('invoice_risk_level'),
    },
    fieldName: 'riskLevel',
    label: '发票风险等级',
  },
  {
    component: 'Input',
    fieldName: 'issuer',
    label: '开票人',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_STATUS 便于维护
      options: getDictOptions('invoice_status'),
    },
    fieldName: 'invoiceStatus',
    label: '状态',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_SOURCE 便于维护
      options: getDictOptions('invoice_source'),
    },
    fieldName: 'source',
    label: '发票来源',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_TYPE 便于维护
      options: getDictOptions('invoice_type'),
    },
    fieldName: 'type',
    label: '发票类型',
  },
  {
    component: 'Input',
    fieldName: 'invoiceOssUrl',
    label: '发票地址url',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '发票ID',
    field: 'invoiceId',
  },
  {
    title: '老jsj发票id',
    field: 'oldInvoiceId',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '发票代码',
    field: 'invoiceCode',
  },
  {
    title: '发票号码',
    field: 'invoiceNumber',
  },
  {
    title: '数电票号码',
    field: 'digitalInvoiceNumber',
  },
  {
    title: '销方识别号',
    field: 'sellerTaxId',
  },
  {
    title: '销方名称',
    field: 'sellerName',
  },
  {
    title: '购方识别号',
    field: 'buyerTaxId',
  },
  {
    title: '购方名称',
    field: 'buyerName',
  },
  {
    title: '开票日期',
    field: 'invoiceDate',
  },
  {
    title: '金额',
    field: 'amount',
  },
  {
    title: '税额',
    field: 'taxAmount',
  },
  {
    title: '价税合计',
    field: 'totalAmount',
  },
  {
    title: '发票来源',
    field: 'invoiceSource',
  },
  {
    title: '发票票种',
    field: 'invoiceType',
  },
  {
    title: '是否正数发票',
    field: 'isPositiveInvoice',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_ONE_ZERO 便于维护
        return renderDict(row.isPositiveInvoice, 'sys_one_zero');
      },
    },
  },
  {
    title: '发票风险等级',
    field: 'riskLevel',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.INVOICE_RISK_LEVEL 便于维护
        return renderDict(row.riskLevel, 'invoice_risk_level');
      },
    },
  },
  {
    title: '开票人',
    field: 'issuer',
  },
  {
    title: '状态',
    field: 'invoiceStatus',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.INVOICE_STATUS 便于维护
        return renderDict(row.invoiceStatus, 'invoice_status');
      },
    },
  },
  {
    title: '发票来源',
    field: 'source',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.INVOICE_SOURCE 便于维护
        return renderDict(row.source, 'invoice_source');
      },
    },
  },
  {
    title: '发票类型',
    field: 'type',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.INVOICE_TYPE 便于维护
        return renderDict(row.type, 'invoice_type');
      },
    },
  },
  {
    title: '发票地址url',
    field: 'invoiceOssUrl',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '发票ID',
    fieldName: 'invoiceId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '老jsj发票id',
    fieldName: 'oldInvoiceId',
    component: 'Input',
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '发票代码',
    fieldName: 'invoiceCode',
    component: 'Input',
  },
  {
    label: '发票号码',
    fieldName: 'invoiceNumber',
    component: 'Input',
  },
  {
    label: '数电票号码',
    fieldName: 'digitalInvoiceNumber',
    component: 'Input',
  },
  {
    label: '销方识别号',
    fieldName: 'sellerTaxId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '销方名称',
    fieldName: 'sellerName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '购方识别号',
    fieldName: 'buyerTaxId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '购方名称',
    fieldName: 'buyerName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '开票日期',
    fieldName: 'invoiceDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    rules: 'required',
  },
  {
    label: '金额',
    fieldName: 'amount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '税额',
    fieldName: 'taxAmount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '价税合计',
    fieldName: 'totalAmount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '发票来源',
    fieldName: 'invoiceSource',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '发票票种',
    fieldName: 'invoiceType',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '是否正数发票',
    fieldName: 'isPositiveInvoice',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_ONE_ZERO 便于维护
      options: getDictOptions('sys_one_zero'),
    },
    rules: 'selectRequired',
  },
  {
    label: '发票风险等级',
    fieldName: 'riskLevel',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_RISK_LEVEL 便于维护
      options: getDictOptions('invoice_risk_level'),
    },
    rules: 'selectRequired',
  },
  {
    label: '开票人',
    fieldName: 'issuer',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '状态',
    fieldName: 'invoiceStatus',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_STATUS 便于维护
      options: getDictOptions('invoice_status'),
    },
    rules: 'selectRequired',
  },
  {
    label: '发票来源',
    fieldName: 'source',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_SOURCE 便于维护
      options: getDictOptions('invoice_source'),
    },
    rules: 'selectRequired',
  },
  {
    label: '发票类型',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INVOICE_TYPE 便于维护
      options: getDictOptions('invoice_type'),
    },
  },
  {
    label: '发票地址url',
    fieldName: 'invoiceOssUrl',
    component: 'Input',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Input',
  },
];

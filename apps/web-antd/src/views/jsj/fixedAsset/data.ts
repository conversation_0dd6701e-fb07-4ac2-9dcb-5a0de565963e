import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'accumulatedDecrement',
    label: '累计减值金额',
  },
  {
    component: 'Input',
    fieldName: 'accumulatedDepreciationSubject',
    label: '累计折旧科目名称',
  },
  {
    component: 'Input',
    fieldName: 'accumulatedDepreciation',
    label: '累计折旧金额',
  },
  {
    component: 'Input',
    fieldName: 'assetCode',
    label: '资产编码',
  },
  {
    component: 'Input',
    fieldName: 'assetName',
    label: '资产名称',
  },
  {
    component: 'Input',
    fieldName: 'assetType',
    label: '资产类型',
  },
  {
    component: 'Input',
    fieldName: 'beginDepreciation',
    label: '开始折旧金额',
  },
  {
    component: 'Input',
    fieldName: 'originalBeginDepreciation',
    label: '原始开始折旧金额',
  },
  {
    component: 'Input',
    fieldName: 'beginNet',
    label: '初始净值',
  },
  {
    component: 'Input',
    fieldName: 'cardCode',
    label: '卡片编号',
  },
  {
    component: 'Input',
    fieldName: 'clearCost',
    label: '清理成本',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'clearDate',
    label: '清理日期',
  },
  {
    component: 'Input',
    fieldName: 'clearIncome',
    label: '清理收入',
  },
  {
    component: 'Input',
    fieldName: 'clearReason',
    label: '清理原因',
  },
  {
    component: 'Input',
    fieldName: 'clearVat',
    label: '清理增值税',
  },
  {
    component: 'Input',
    fieldName: 'decrementOtherSubject',
    label: '其他减值科目',
  },
  {
    component: 'Input',
    fieldName: 'decrementProvisions',
    label: '减值准备金额',
  },
  {
    component: 'Input',
    fieldName: 'decrementSubject',
    label: '减值科目名称',
  },
  {
    component: 'Input',
    fieldName: 'depreciationMethod',
    label: '折旧方法',
  },
  {
    component: 'Input',
    fieldName: 'depreciationPeriod',
    label: '折旧年限',
  },
  {
    component: 'Input',
    fieldName: 'depreciationSubject',
    label: '折旧科目名称',
  },
  {
    component: 'Input',
    fieldName: 'deptId',
    label: '所属部门ID',
  },
  {
    component: 'Input',
    fieldName: 'assetSubject',
    label: '资产科目名称',
  },
  {
    component: 'Select',
    componentProps: {},
    fieldName: 'increaseType',
    label: '增加方式',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'inputTime',
    label: '录入时间',
  },
  {
    component: 'Input',
    fieldName: 'accountYear',
    label: '年份',
  },
  {
    component: 'Input',
    fieldName: 'accountMonth',
    label: '月份',
  },
  {
    component: 'Input',
    fieldName: 'monthDepreciation',
    label: '本月折旧金额',
  },
  {
    component: 'Input',
    fieldName: 'netValue',
    label: '净值',
  },
  {
    component: 'Input',
    fieldName: 'note',
    label: '备注',
  },
  {
    component: 'Input',
    fieldName: 'originalValue',
    label: '原始价值',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'purchaseDate',
    label: '购置日期',
  },
  {
    component: 'Input',
    fieldName: 'residualRate',
    label: '残值率',
  },
  {
    component: 'Input',
    fieldName: 'residualValue',
    label: '残值',
  },
  {
    component: 'Input',
    fieldName: 'storePlace',
    label: '存放地点',
  },
  {
    component: 'Input',
    fieldName: 'thisMonthDepreciation',
    label: '本月折旧金额',
  },
  {
    component: 'Input',
    fieldName: 'totalValue',
    label: '总价值',
  },
  {
    component: 'Input',
    fieldName: 'usageInfo',
    label: '使用情况',
  },
  {
    component: 'Input',
    fieldName: 'valueAddedTax',
    label: '增值税',
  },
  {
    component: 'Input',
    fieldName: 'count',
    label: '数量',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '累计减值金额',
    field: 'accumulatedDecrement',
  },
  {
    title: '累计折旧科目名称',
    field: 'accumulatedDepreciationSubject',
  },
  {
    title: '累计折旧金额',
    field: 'accumulatedDepreciation',
  },
  {
    title: '是否生成凭证(0-否 1-是)',
    field: 'isAddVoucher',
  },
  {
    title: '资产编码',
    field: 'assetCode',
  },
  {
    title: '资产名称',
    field: 'assetName',
  },
  {
    title: '资产类型',
    field: 'assetType',
  },
  {
    title: '开始折旧金额',
    field: 'beginDepreciation',
  },
  {
    title: '原始开始折旧金额',
    field: 'originalBeginDepreciation',
  },
  {
    title: '初始净值',
    field: 'beginNet',
  },
  {
    title: '卡片编号',
    field: 'cardCode',
  },
  {
    title: '清理成本',
    field: 'clearCost',
  },
  {
    title: '清理日期',
    field: 'clearDate',
  },
  {
    title: '清理收入',
    field: 'clearIncome',
  },
  {
    title: '清理原因',
    field: 'clearReason',
  },
  {
    title: '清理增值税',
    field: 'clearVat',
  },
  {
    title: '其他减值科目',
    field: 'decrementOtherSubject',
  },
  {
    title: '减值准备金额',
    field: 'decrementProvisions',
  },
  {
    title: '减值科目名称',
    field: 'decrementSubject',
  },
  {
    title: '折旧方法',
    field: 'depreciationMethod',
  },
  {
    title: '折旧年限',
    field: 'depreciationPeriod',
  },
  {
    title: '折旧科目名称',
    field: 'depreciationSubject',
  },
  {
    title: '所属部门ID',
    field: 'deptId',
  },
  {
    title: '资产科目名称',
    field: 'assetSubject',
  },
  {
    title: '增加方式',
    field: 'increaseType',
  },
  {
    title: '录入时间',
    field: 'inputTime',
  },
  {
    title: '年份',
    field: 'accountYear',
  },
  {
    title: '月份',
    field: 'accountMonth',
  },
  {
    title: '本月折旧金额',
    field: 'monthDepreciation',
  },
  {
    title: '净值',
    field: 'netValue',
  },
  {
    title: '备注',
    field: 'note',
  },
  {
    title: '原始价值',
    field: 'originalValue',
  },
  {
    title: '购置日期',
    field: 'purchaseDate',
  },
  {
    title: '残值率',
    field: 'residualRate',
  },
  {
    title: '残值',
    field: 'residualValue',
  },
  {
    title: '存放地点',
    field: 'storePlace',
  },
  {
    title: '本月折旧金额',
    field: 'thisMonthDepreciation',
  },
  {
    title: '总价值',
    field: 'totalValue',
  },
  {
    title: '使用情况',
    field: 'usageInfo',
  },
  {
    title: '增值税',
    field: 'valueAddedTax',
  },
  {
    title: '数量',
    field: 'count',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '累计减值金额',
    fieldName: 'accumulatedDecrement',
    component: 'Input',
  },
  {
    label: '累计折旧科目名称',
    fieldName: 'accumulatedDepreciationSubject',
    component: 'Input',
  },
  {
    label: '累计折旧金额',
    fieldName: 'accumulatedDepreciation',
    component: 'Input',
  },
  {
    label: '资产编码',
    fieldName: 'assetCode',
    component: 'Input',
  },
  {
    label: '资产名称',
    fieldName: 'assetName',
    component: 'Input',
  },
  {
    label: '资产类型',
    fieldName: 'assetType',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '开始折旧金额',
    fieldName: 'beginDepreciation',
    component: 'Input',
  },
  {
    label: '原始开始折旧金额',
    fieldName: 'originalBeginDepreciation',
    component: 'Input',
  },
  {
    label: '初始净值',
    fieldName: 'beginNet',
    component: 'Input',
  },
  {
    label: '卡片编号',
    fieldName: 'cardCode',
    component: 'Input',
  },
  {
    label: '清理成本',
    fieldName: 'clearCost',
    component: 'Input',
  },
  {
    label: '清理日期',
    fieldName: 'clearDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '清理收入',
    fieldName: 'clearIncome',
    component: 'Input',
  },
  {
    label: '清理原因',
    fieldName: 'clearReason',
    component: 'Input',
  },
  {
    label: '清理增值税',
    fieldName: 'clearVat',
    component: 'Input',
  },
  {
    label: '其他减值科目',
    fieldName: 'decrementOtherSubject',
    component: 'Input',
  },
  {
    label: '减值准备金额',
    fieldName: 'decrementProvisions',
    component: 'Input',
  },
  {
    label: '减值科目名称',
    fieldName: 'decrementSubject',
    component: 'Input',
  },
  {
    label: '折旧方法',
    fieldName: 'depreciationMethod',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '折旧年限',
    fieldName: 'depreciationPeriod',
    component: 'Input',
  },
  {
    label: '折旧科目名称',
    fieldName: 'depreciationSubject',
    component: 'Input',
  },
  {
    label: '所属部门ID',
    fieldName: 'deptId',
    component: 'Input',
  },
  {
    label: '资产科目名称',
    fieldName: 'assetSubject',
    component: 'Input',
  },
  {
    label: '增加方式',
    fieldName: 'increaseType',
    component: 'Select',
    componentProps: {},
  },
  {
    label: '录入时间',
    fieldName: 'inputTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '年份',
    fieldName: 'accountYear',
    component: 'Input',
  },
  {
    label: '月份',
    fieldName: 'accountMonth',
    component: 'Input',
  },
  {
    label: '本月折旧金额',
    fieldName: 'monthDepreciation',
    component: 'Input',
  },
  {
    label: '净值',
    fieldName: 'netValue',
    component: 'Input',
  },
  {
    label: '备注',
    fieldName: 'note',
    component: 'Input',
  },
  {
    label: '原始价值',
    fieldName: 'originalValue',
    component: 'Input',
  },
  {
    label: '购置日期',
    fieldName: 'purchaseDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '残值率',
    fieldName: 'residualRate',
    component: 'Input',
  },
  {
    label: '残值',
    fieldName: 'residualValue',
    component: 'Input',
  },
  {
    label: '存放地点',
    fieldName: 'storePlace',
    component: 'Input',
  },
  {
    label: '本月折旧金额',
    fieldName: 'thisMonthDepreciation',
    component: 'Input',
  },
  {
    label: '总价值',
    fieldName: 'totalValue',
    component: 'Input',
  },
  {
    label: '使用情况',
    fieldName: 'usageInfo',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '增值税',
    fieldName: 'valueAddedTax',
    component: 'Input',
  },
  {
    label: '数量',
    fieldName: 'count',
    component: 'Input',
  },
];

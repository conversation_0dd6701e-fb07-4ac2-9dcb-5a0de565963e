import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'oldInvoiceDetailId',
    label: '老jsj发票明细ID',
  },
  {
    component: 'Input',
    fieldName: 'invoiceId',
    label: '发票ID',
  },
  {
    component: 'Input',
    fieldName: 'taxClassificationCode',
    label: '税收分类编码',
  },
  {
    component: 'Input',
    fieldName: 'specialBusinessType',
    label: '特定业务类型',
  },
  {
    component: 'Input',
    fieldName: 'goodsOrServiceName',
    label: '货物或应税劳务名称',
  },
  {
    component: 'Input',
    fieldName: 'specificationModel',
    label: '规格型号',
  },
  {
    component: 'Input',
    fieldName: 'unit',
    label: '单位',
  },
  {
    component: 'Input',
    fieldName: 'quantity',
    label: '数量',
  },
  {
    component: 'Input',
    fieldName: 'unitPrice',
    label: '单价',
  },
  {
    component: 'Input',
    fieldName: 'amount',
    label: '金额',
  },
  {
    component: 'Input',
    fieldName: 'taxRate',
    label: '税率',
  },
  {
    component: 'Input',
    fieldName: 'taxAmount',
    label: '税额',
  },
  {
    component: 'Input',
    fieldName: 'totalAmount',
    label: '价税合计',
  },
  {
    component: 'Input',
    fieldName: 'financialScenario',
    label: '资金场景',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '发票明细ID',
    field: 'invoiceDetailId',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '老jsj发票明细ID',
    field: 'oldInvoiceDetailId',
  },
  {
    title: '发票ID',
    field: 'invoiceId',
  },
  {
    title: '税收分类编码',
    field: 'taxClassificationCode',
  },
  {
    title: '特定业务类型',
    field: 'specialBusinessType',
  },
  {
    title: '货物或应税劳务名称',
    field: 'goodsOrServiceName',
  },
  {
    title: '规格型号',
    field: 'specificationModel',
  },
  {
    title: '单位',
    field: 'unit',
  },
  {
    title: '数量',
    field: 'quantity',
  },
  {
    title: '单价',
    field: 'unitPrice',
  },
  {
    title: '金额',
    field: 'amount',
  },
  {
    title: '税率',
    field: 'taxRate',
  },
  {
    title: '税额',
    field: 'taxAmount',
  },
  {
    title: '价税合计',
    field: 'totalAmount',
  },
  {
    title: '资金场景',
    field: 'financialScenario',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '发票明细ID',
    fieldName: 'invoiceDetailId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '老jsj发票明细ID',
    fieldName: 'oldInvoiceDetailId',
    component: 'Input',
  },
  {
    label: '发票ID',
    fieldName: 'invoiceId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '税收分类编码',
    fieldName: 'taxClassificationCode',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '特定业务类型',
    fieldName: 'specialBusinessType',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '货物或应税劳务名称',
    fieldName: 'goodsOrServiceName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '规格型号',
    fieldName: 'specificationModel',
    component: 'Input',
  },
  {
    label: '单位',
    fieldName: 'unit',
    component: 'Input',
  },
  {
    label: '数量',
    fieldName: 'quantity',
    component: 'Input',
  },
  {
    label: '单价',
    fieldName: 'unitPrice',
    component: 'Input',
  },
  {
    label: '金额',
    fieldName: 'amount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '税率',
    fieldName: 'taxRate',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '税额',
    fieldName: 'taxAmount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '价税合计',
    fieldName: 'totalAmount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '资金场景',
    fieldName: 'financialScenario',
    component: 'Input',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Input',
  },
];

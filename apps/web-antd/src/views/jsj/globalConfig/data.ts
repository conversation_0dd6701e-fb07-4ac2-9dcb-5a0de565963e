import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'configType',
    label: '参数名称',
  },
  {
    component: 'Input',
    fieldName: 'configKey',
    label: '参数键名',
  },
  {
    component: 'Textarea',
    fieldName: 'configValue',
    label: '参数键值',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '参数主键',
    field: 'configId',
  },
  {
    title: '参数名称',
    field: 'configType',
  },
  {
    title: '参数键名',
    field: 'configKey',
  },
  {
    title: '参数键值',
    field: 'configValue',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '参数主键',
    fieldName: 'configId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '参数名称',
    fieldName: 'configType',
    component: 'Input',
  },
  {
    label: '参数键名',
    fieldName: 'configKey',
    component: 'Input',
  },
  {
    label: '参数键值',
    fieldName: 'configValue',
    component: 'Textarea',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Textarea',
  },
];

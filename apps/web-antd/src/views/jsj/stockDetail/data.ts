import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'stockId',
    label: '库存关联ID',
  },
  {
    component: 'Input',
    fieldName: 'num',
    label: '数量',
  },
  {
    component: 'Input',
    fieldName: 'price',
    label: '单价',
  },
  {
    component: 'Input',
    fieldName: 'notTaxMoney',
    label: '不含税金额',
  },
  {
    component: 'Input',
    fieldName: 'taxRate',
    label: '税率',
  },
  {
    component: 'Input',
    fieldName: 'taxMoney',
    label: '税额',
  },
  {
    component: 'Input',
    fieldName: 'totalMoneyAmount',
    label: '价税合计',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '',
    field: 'id',
  },
  {
    title: '库存关联ID',
    field: 'stockId',
  },
  {
    title: '数量',
    field: 'num',
  },
  {
    title: '单价',
    field: 'price',
  },
  {
    title: '不含税金额',
    field: 'notTaxMoney',
  },
  {
    title: '税率',
    field: 'taxRate',
  },
  {
    title: '税额',
    field: 'taxMoney',
  },
  {
    title: '价税合计',
    field: 'totalMoneyAmount',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '库存关联ID',
    fieldName: 'stockId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '数量',
    fieldName: 'num',
    component: 'Input',
  },
  {
    label: '单价',
    fieldName: 'price',
    component: 'Input',
  },
  {
    label: '不含税金额',
    fieldName: 'notTaxMoney',
    component: 'Input',
  },
  {
    label: '税率',
    fieldName: 'taxRate',
    component: 'Input',
  },
  {
    label: '税额',
    fieldName: 'taxMoney',
    component: 'Input',
  },
  {
    label: '价税合计',
    fieldName: 'totalMoneyAmount',
    component: 'Input',
  },
];

import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

// import { getDictOptions } from '#/utils/dict'; // 已删除dict工具
import { renderDict } from '#/utils/render';

// 临时空函数，字典功能已移除
const getDictOptions = () => [];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'attachNum',
    label: '附件数量',
  },
  {
    component: 'Textarea',
    fieldName: 'auditComment',
    label: '审核意见',
  },
  {
    component: 'Input',
    fieldName: 'auditorId',
    label: '审核人ID',
  },
  {
    component: 'Input',
    fieldName: 'creditTotal',
    label: '贷方总额',
  },
  {
    component: 'Input',
    fieldName: 'debitTotal',
    label: '借方总额',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'voucherDate',
    label: '凭证日期',
  },
  {
    component: 'Input',
    fieldName: 'voucherMonth',
    label: '所属月份',
  },
  {
    component: 'Input',
    fieldName: 'voucherNo',
    label: '凭证编号',
  },
  {
    component: 'Input',
    fieldName: 'voucherWord',
    label: '凭证字',
  },
  {
    component: 'Input',
    fieldName: 'voucherYear',
    label: '凭证所属年份',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.VOUCHER_TYPE 便于维护
      options: getDictOptions('voucher_type'),
    },
    fieldName: 'type',
    label: '凭证类型',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '附件数量',
    field: 'attachNum',
  },
  {
    title: '审核意见',
    field: 'auditComment',
  },
  {
    title: '审核人ID',
    field: 'auditorId',
  },
  {
    title: '贷方总额',
    field: 'creditTotal',
  },
  {
    title: '借方总额',
    field: 'debitTotal',
  },
  {
    title: '凭证日期',
    field: 'voucherDate',
  },
  {
    title: '所属月份',
    field: 'voucherMonth',
  },
  {
    title: '凭证编号',
    field: 'voucherNo',
  },
  {
    title: '凭证字',
    field: 'voucherWord',
  },
  {
    title: '凭证所属年份',
    field: 'voucherYear',
  },
  {
    title: '凭证类型',
    field: 'type',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.VOUCHER_TYPE 便于维护
        return renderDict(row.type, 'voucher_type');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '附件数量',
    fieldName: 'attachNum',
    component: 'Input',
  },
  {
    label: '审核意见',
    fieldName: 'auditComment',
    component: 'Textarea',
  },
  {
    label: '审核人ID',
    fieldName: 'auditorId',
    component: 'Input',
  },
  {
    label: '贷方总额',
    fieldName: 'creditTotal',
    component: 'Input',
  },
  {
    label: '借方总额',
    fieldName: 'debitTotal',
    component: 'Input',
  },
  {
    label: '凭证日期',
    fieldName: 'voucherDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    rules: 'required',
  },
  {
    label: '所属月份',
    fieldName: 'voucherMonth',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '凭证编号',
    fieldName: 'voucherNo',
    component: 'Input',
  },
  {
    label: '凭证字',
    fieldName: 'voucherWord',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '凭证所属年份',
    fieldName: 'voucherYear',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '凭证类型',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.VOUCHER_TYPE 便于维护
      options: getDictOptions('voucher_type'),
    },
  },
];

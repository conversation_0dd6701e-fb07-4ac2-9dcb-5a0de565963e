<script setup lang="ts">
import type { VoucherDetail } from '#/store/modules/voucher';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import {
  CopyOutlined,
  DeleteOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import { Select } from 'ant-design-vue';

import { useVoucherStore } from '#/store/modules/voucher';

interface VoucherItem {
  key: number;
  summary: string;
  accountCode: string;
  debitAmount: string;
  creditAmount: string;
  debitIptVal: string;
  creditIptVal: string;
  showDebitInput: boolean;
  showCreditInput: boolean;
}

interface SelectOption {
  label: string;
  value: string;
}

// 摘要选项
const summaryOptions = ref<SelectOption[]>([
  { label: '4月收入', value: '4月收入' },
  { label: '销售收入', value: '销售收入' },
  { label: '采购支出', value: '采购支出' },
]);

// 会计科目选项
const accountOptions = ref<SelectOption[]>([
  { label: '1122 应收账款', value: '1122 应收账款' },
  { label: '1001 库存现金', value: '1001 库存现金' },
  { label: '2202 应付账款', value: '2202 应付账款' },
]);

const units = [
  '十',
  '亿',
  '千',
  '百',
  '十',
  '万',
  '千',
  '百',
  '十',
  '元',
  '角',
  '分',
];

const createEmptyItem = (key: number): VoucherItem => ({
  key,
  summary: '',
  accountCode: '',
  debitAmount: '',
  creditAmount: '',
  debitIptVal: '',
  creditIptVal: '',
  showDebitInput: false,
  showCreditInput: false,
});

const voucherStore = useVoucherStore();

// 初始化5行数据
const voucherItems = ref<VoucherItem[]>(
  Array.from({ length: 5 }, (_, index) => createEmptyItem(index + 1)),
);

// 处理从 store 获取的凭证数据
onMounted(() => {
  const voucherData = voucherStore.currentVoucher;
  if (voucherData?.vouchers?.[0]?.details) {
    const details = voucherData.vouchers[0].details;
    voucherItems.value = details.map(
      (detail: VoucherDetail, index: number) => ({
        key: index + 1,
        summary: detail.summary,
        accountCode: detail.account,
        debitAmount: detail.debit.toString(),
        creditAmount: detail.credit.toString(),
        debitIptVal: '',
        creditIptVal: '',
        showDebitInput: false,
        showCreditInput: false,
      }),
    );
  }
});

let nextKey = 6; // 从6开始，因为已经用了1-5

const addRow = (index: number) => {
  voucherItems.value.splice(index + 1, 0, createEmptyItem(nextKey++));
};

const copyRow = (index: number) => {
  const currentItem = voucherItems.value[index];
  if (!currentItem) return;

  const newItem: VoucherItem = {
    key: nextKey++,
    summary: currentItem.summary,
    accountCode: currentItem.accountCode,
    debitAmount: currentItem.debitAmount,
    creditAmount: currentItem.creditAmount,
    debitIptVal: '',
    creditIptVal: '',
    showDebitInput: false,
    showCreditInput: false,
  };
  voucherItems.value.splice(index + 1, 0, newItem);
};

const deleteRow = (index: number) => {
  if (voucherItems.value.length > 1) {
    voucherItems.value.splice(index, 1);
  }
};

const totalDebit = computed(() => {
  return voucherItems.value.reduce(
    (sum, item) => sum + Number(item.debitAmount || 0),
    0,
  );
});
const totalCredit = computed(() => {
  return voucherItems.value.reduce(
    (sum, item) => sum + Number(item.creditAmount || 0),
    0,
  );
});

function getAmountDigit(amount: number | string, pos: number) {
  // pos: 1~12, 从高位到低位
  if (!amount) return '';

  // 将数字转换为字符串，如果是整数，自动添加两位小数
  let str = Number(amount).toFixed(2).replace('.', '');

  // 移除前导零
  str = str.replace(/^0+/, '');

  // 补齐12位
  str = str.padStart(12, ' ');

  return str[pos - 1] === ' ' ? '' : str[pos - 1];
}

const currentItem = ref<any>(null);

watch(
  () => currentItem.value?.debitIptVal,
  (newVal) => {
    if (!currentItem.value || newVal === undefined) return;

    // 只允许输入数字和小数点
    let value = newVal.replaceAll(/[^\d.]/g, '');

    // 确保只有一个小数点
    const parts = value.split('.');
    if (parts.length > 2) {
      value = `${parts[0]}.${parts.slice(1).join('')}`;
    }

    // 限制小数点后最多两位
    if (parts.length === 2 && parts[1] && parts[1].length > 2) {
      value = `${parts[0]}.${parts[1].slice(0, 2)}`;
    }

    // 如果值发生了变化，更新输入值
    if (value !== newVal) {
      currentItem.value.debitIptVal = value;
    }
  },
);

const handleBlur = (
  item: VoucherItem,
  type: 'credit' | 'debit',
  e: FocusEvent,
) => {
  // 检查是否点击了同一个输入框内部
  const relatedTarget = e.relatedTarget as HTMLElement;
  if (relatedTarget?.classList.contains(`${type}-input`)) {
    return;
  }

  // 解析输入的金额
  const value = type === 'debit' ? item.debitIptVal : item.creditIptVal;
  let amount = Number.parseFloat(value || '0');

  if (Number.isNaN(amount)) {
    if (type === 'debit') {
      item.debitAmount = '';
      item.debitIptVal = '';
      item.showDebitInput = false;
    } else {
      item.creditAmount = '';
      item.creditIptVal = '';
      item.showCreditInput = false;
    }
  } else {
    // 根据是否为整数处理小数位
    amount = Number.isInteger(amount) ? amount * 100 : Math.round(amount * 100);
    const formattedAmount = (amount / 100).toFixed(2);

    if (type === 'debit') {
      // 如果输入借方金额，清空贷方金额
      item.debitAmount = formattedAmount;
      item.creditAmount = '';
      item.debitIptVal = '';
      item.showDebitInput = false;
    } else {
      // 如果输入贷方金额，清空借方金额
      item.creditAmount = formattedAmount;
      item.debitAmount = '';
      item.creditIptVal = '';
      item.showCreditInput = false;
    }
  }
};

const priceClick = (item: VoucherItem, type: 'credit' | 'debit') => {
  // 如果已经有金额，不要清空，而是显示在输入框中
  if (type === 'debit') {
    item.showDebitInput = true;
    item.debitIptVal = item.debitAmount || '';
    item.showCreditInput = false;
  } else {
    item.showCreditInput = true;
    item.creditIptVal = item.creditAmount || '';
    item.showDebitInput = false;
  }

  // 重置其他行的状态
  voucherItems.value.forEach((row) => {
    if (row !== item) {
      row.showDebitInput = false;
      row.showCreditInput = false;
    }
  });

  currentItem.value = item;

  // 确保DOM更新后再设置焦点
  nextTick(() => {
    const input = document.querySelector(`.${type}-input`);
    if (input) {
      (input as HTMLInputElement).focus();
      // 将光标移到最后
      const len = (input as HTMLInputElement).value.length;
      (input as HTMLInputElement).setSelectionRange(len, len);
    }
  });
};

// 添加顶部数据
const voucherType = ref('现付');
const voucherNo = ref('002');
const voucherDate = ref<string>('2020-01-04');
const attachmentCount = ref(0);
</script>

<template>
  <div class="voucher-wrapper">
    <div class="voucher-content">
      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <a-select v-model:value="voucherType" style="width: 100px">
            <a-select-option value="现付">现付</a-select-option>
            <a-select-option value="转账">转账</a-select-option>
          </a-select>
          <a-input v-model:value="voucherNo" style="width: 80px" />
          <span>号</span>
          <span class="label">凭证日期</span>
          <!-- <a-date-picker
            v-model:value="voucherDate"
            format="YYYY-MM-DD"
            :bordered="false"
            :suffix-icon="h(CalendarOutlined)"
          /> -->
          <span class="label">附件数量</span>
          <a-input-number
            v-model:value="attachmentCount"
            :min="0"
            style="width: 80px"
          />
          <a-button type="link">附件</a-button>
        </div>
        <div class="toolbar-right">
          <a-button type="primary">保存</a-button>
          <a-button type="primary">保存并新增</a-button>
          <a-button>使用模版</a-button>
          <a-button>保存模版</a-button>
        </div>
      </div>

      <!-- 表头 -->
      <div class="voucher-header">
        <div class="header-row">
          <div class="cell cell-row-num" rowspan="2">行次</div>
          <div class="cell cell-summary" rowspan="2">摘要</div>
          <div class="cell cell-account" rowspan="2">会计科目</div>
          <div class="cell cell-amount">
            <div class="amount-header">
              <div class="amount-title">借方金额</div>
              <div class="amount-units">
                <div
                  v-for="unit in units"
                  :key="`debit-${unit}`"
                  class="amount-unit"
                >
                  {{ unit }}
                </div>
              </div>
            </div>
          </div>
          <div class="cell cell-amount">
            <div class="amount-header">
              <div class="amount-title">贷方金额</div>
              <div class="amount-units">
                <div
                  v-for="unit in units"
                  :key="`credit-${unit}`"
                  class="amount-unit"
                >
                  {{ unit }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区 -->
      <div class="voucher-body">
        <div
          class="voucher-row"
          v-for="(item, idx) in voucherItems"
          :key="item.key"
        >
          <div class="front-buttons">
            <a-button type="link" size="small" @click="addRow(idx)">
              <PlusOutlined />
            </a-button>
            <a-button type="link" size="small" @click="copyRow(idx)">
              <CopyOutlined />
            </a-button>
          </div>
          <div class="row-content">
            <div class="cell cell-row-num">
              {{ idx + 1 }}
            </div>
            <div class="cell cell-summary">
              <Select
                v-model:value="item.summary"
                :options="summaryOptions"
                show-search
                allow-clear
                mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
                class="custom-select"
              />
            </div>
            <div class="cell cell-account">
              <Select
                v-model:value="item.accountCode"
                :options="accountOptions"
                show-search
                allow-clear
                mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
                class="custom-select"
              />
            </div>
            <!-- 借方金额 -->
            <div class="cell cell-amount">
              <template v-if="item.showDebitInput">
                <div class="amount-input-container">
                  <input
                    v-model="item.debitIptVal"
                    class="amount-input debit-input"
                    @blur="handleBlur(item, 'debit', $event)"
                    type="text"
                  />
                </div>
              </template>
              <template v-else>
                <div
                  class="amount-digits debit-digits"
                  @click="priceClick(item, 'debit')"
                >
                  <div
                    v-for="i in 12"
                    :key="`debit-${i}`"
                    class="amount-digit debit-stripe"
                    :class="[
                      i === 9 ? 'unit-yuan' : '',
                      i > 9 ? 'unit-jf' : '',
                    ]"
                  >
                    {{ getAmountDigit(item.debitAmount, i) }}
                  </div>
                </div>
              </template>
            </div>
            <!-- 贷方金额 -->
            <div class="cell cell-amount">
              <template v-if="item.showCreditInput">
                <div class="amount-input-container">
                  <input
                    v-model="item.creditIptVal"
                    class="amount-input credit-input"
                    @blur="handleBlur(item, 'credit', $event)"
                    type="text"
                  />
                </div>
              </template>
              <template v-else>
                <div
                  class="amount-digits credit-digits"
                  @click="priceClick(item, 'credit')"
                >
                  <div
                    v-for="i in 12"
                    :key="`credit-${i}`"
                    class="amount-digit credit-stripe"
                    :class="[
                      i === 9 ? 'unit-yuan' : '',
                      i > 9 ? 'unit-jf' : '',
                    ]"
                  >
                    {{ getAmountDigit(item.creditAmount, i) }}
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="back-buttons">
            <a-button
              type="link"
              size="small"
              @click="deleteRow(idx)"
              :disabled="voucherItems.length === 1"
            >
              <DeleteOutlined />
            </a-button>
          </div>
        </div>

        <!-- 合计行 -->
        <div class="voucher-row total-row">
          <div class="row-content">
            <div class="cell cell-total">合计：</div>
            <div class="cell cell-amount">
              <div class="amount-digits">
                <div
                  v-for="i in 12"
                  :key="`debit-total-${i}`"
                  class="amount-digit debit-stripe"
                >
                  {{ getAmountDigit(totalDebit, i) }}
                </div>
              </div>
            </div>
            <div class="cell cell-amount">
              <div class="amount-digits">
                <div
                  v-for="i in 12"
                  :key="`credit-total-${i}`"
                  class="amount-digit credit-stripe"
                >
                  {{ getAmountDigit(totalCredit, i) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.voucher-wrapper {
  min-width: 1200px;
  padding: 24px;
  overflow-x: auto;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
}

.voucher-content {
  position: relative;
  width: 100%;
  padding: 20px;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 20px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  .toolbar-left {
    display: flex;
    gap: 8px;
    align-items: center;

    .label {
      margin: 0 4px;
      color: #666;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
  }
}

.voucher-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #fff;
}

.header-row {
  display: flex;
  border: 1px solid #e6e6e6;
  border-bottom: none;
}

.voucher-body {
  margin: 0 -40px;
}

.voucher-row {
  position: relative;
  padding: 0 40px;
  margin-bottom: -1px;

  &:hover {
    .front-buttons,
    .back-buttons {
      display: flex;
      align-items: center;
    }
  }
}

.row-content {
  display: flex;
  width: 100%;
  border: 1px solid #e6e6e6;
}

.front-buttons {
  position: absolute;
  top: 50%;
  left: -30px;
  z-index: 1;
  display: none;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transform: translateY(-50%);

  .ant-btn {
    padding: 0 4px;
    color: #666;

    &:hover {
      color: #1890ff;
    }

    &[disabled] {
      color: #d9d9d9;
    }
  }
}

.back-buttons {
  position: absolute;
  top: 50%;
  right: -10px;
  z-index: 1;
  display: none;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transform: translateY(-50%);

  .ant-btn {
    padding: 0 4px;
    color: #666;

    &:hover {
      color: #ff4d4f;
    }

    &[disabled] {
      color: #d9d9d9;
    }
  }
}

.cell {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  font-size: 14px;
  text-align: center;
  background: #fff;
  border-right: 1px solid #e6e6e6;

  &[rowspan='2'] {
    height: 76px;
  }

  &:last-child {
    border-right: none;
  }

  &-row-num {
    position: relative;
    width: 4%;
  }

  &-summary {
    width: 18%;
  }

  &-account {
    width: 38%;
  }

  &-amount {
    width: 20%;
    padding: 0;
  }

  &-total {
    justify-content: flex-start;
    width: 60%;
    padding-left: 8px;
  }
}

.amount-units {
  display: flex;
  width: 100%;
  border-top: 1px solid #e6e6e6;
}

.amount-unit {
  width: calc(100% / 12);
  padding: 4px 0;
  font-size: 12px;
  color: #888;
  text-align: center;
  background: #f8f8f8;
  border-right: 1px solid #e6e6e6;

  &:last-child {
    border-right: none;
  }
}

.amount-digits {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.amount-digit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% / 12);
  height: 100%;
  background: repeating-linear-gradient(
    to right,
    #fff 0,
    #fff 20px,
    #e6f7ff 20px,
    #e6f7ff 24px
  );
  border-right: 1px solid #e6e6e6;

  &:nth-child(7) {
    border-right-color: #a1c6ed;
  }

  &:nth-child(4) {
    border-right-color: #a1c6ed;
  }

  &:nth-child(9) {
    border-right-color: #f2a5a3;
  }
}

.unit-jf {
  background: #fafafa;
}

.amount-input-container {
  width: 100%;
  height: 100%;
  background: #f0f8ff;
}

.amount-input {
  width: 100%;
  height: 100%;
  padding: 0 8px;
  text-align: right;
  background: transparent;
  border: none;
  outline: none;
}

.debit-stripe {
  &::after {
    background-color: #91d5ff;
  }
}

.credit-stripe {
  &::after {
    background-color: #ff4d4f;
  }
}

.custom-select {
  width: 100% !important;

  :deep(.ant-select-selector) {
    height: 32px !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  :deep(.ant-select-selection-search) {
    inset-inline-start: 0 !important;
  }
}

.total-row {
  font-weight: bold;

  .row-content {
    background: #f6ffed;
  }
}

.amount-header {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.amount-title {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 4px;
}
</style>

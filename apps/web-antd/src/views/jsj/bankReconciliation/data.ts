import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'balance',
    label: '账户余额',
  },
  {
    component: 'Input',
    fieldName: 'bankName',
    label: '银行名称',
  },
  {
    component: 'Input',
    fieldName: 'bankCode',
    label: '银行编码',
  },
  {
    component: 'Input',
    fieldName: 'expenditure',
    label: '支出金额',
  },
  {
    component: 'Input',
    fieldName: 'income',
    label: '收入金额',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'tradingTime',
    label: '银行系统记录的交易时间',
  },
  {
    component: 'Input',
    fieldName: 'month',
    label: '对账单所属月份',
  },
  {
    component: 'Input',
    fieldName: 'recNum',
    label: '对账单编号',
  },
  {
    component: 'Input',
    fieldName: 'summary',
    label: '交易摘要',
  },
  {
    component: 'Input',
    fieldName: 'type',
    label: '收支类型',
  },
  {
    component: 'Input',
    fieldName: 'unit',
    label: '往来单位',
  },
  {
    component: 'Input',
    fieldName: 'year',
    label: '对账单所属年份',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '账户余额',
    field: 'balance',
  },
  {
    title: '银行名称',
    field: 'bankName',
  },
  {
    title: '银行编码',
    field: 'bankCode',
  },
  {
    title: '支出金额',
    field: 'expenditure',
  },
  {
    title: '收入金额',
    field: 'income',
  },
  {
    title: '银行系统记录的交易时间',
    field: 'tradingTime',
  },
  {
    title: '对账单所属月份',
    field: 'month',
  },
  {
    title: '对账单编号',
    field: 'recNum',
  },
  {
    title: '交易摘要',
    field: 'summary',
  },
  {
    title: '收支类型',
    field: 'type',
  },
  {
    title: '往来单位',
    field: 'unit',
  },
  {
    title: '凭证ID',
    field: 'voucherId',
  },
  {
    title: '对账单所属年份',
    field: 'year',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '账户余额',
    fieldName: 'balance',
    component: 'Input',
  },
  {
    label: '银行名称',
    fieldName: 'bankName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '银行编码',
    fieldName: 'bankCode',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '支出金额',
    fieldName: 'expenditure',
    component: 'Input',
  },
  {
    label: '收入金额',
    fieldName: 'income',
    component: 'Input',
  },
  {
    label: '银行系统记录的交易时间',
    fieldName: 'tradingTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '对账单所属月份',
    fieldName: 'month',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '对账单编号',
    fieldName: 'recNum',
    component: 'Input',
  },
  {
    label: '交易摘要',
    fieldName: 'summary',
    component: 'Input',
  },
  {
    label: '收支类型',
    fieldName: 'type',
    component: 'Input',
  },
  {
    label: '往来单位',
    fieldName: 'unit',
    component: 'Input',
  },
  {
    label: '对账单所属年份',
    fieldName: 'year',
    component: 'Input',
    rules: 'required',
  },
];

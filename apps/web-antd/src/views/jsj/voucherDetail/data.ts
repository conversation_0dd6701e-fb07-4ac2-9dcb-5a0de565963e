import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'voucherId',
    label: '凭证ID',
  },
  {
    component: 'Input',
    fieldName: 'subject',
    label: '会计科目',
  },
  {
    component: 'Textarea',
    fieldName: 'comment',
    label: '备注',
  },
  {
    component: 'Input',
    fieldName: 'credit',
    label: '贷方金额',
  },
  {
    component: 'Input',
    fieldName: 'debit',
    label: '借方金额',
  },
  {
    component: 'Textarea',
    fieldName: 'summary',
    label: '摘要',
  },
  {
    component: 'Input',
    fieldName: 'idx',
    label: '排序索引',
  },
  {
    component: 'Input',
    fieldName: 'subjectId',
    label: '科目id',
  },
  {
    component: 'Input',
    fieldName: 'auxiliary',
    label: '辅助核算',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '凭证ID',
    field: 'voucherId',
  },
  {
    title: '会计科目',
    field: 'subject',
  },
  {
    title: '备注',
    field: 'comment',
  },
  {
    title: '贷方金额',
    field: 'credit',
  },
  {
    title: '借方金额',
    field: 'debit',
  },
  {
    title: '摘要',
    field: 'summary',
  },
  {
    title: '排序索引',
    field: 'idx',
  },
  {
    title: '科目id',
    field: 'subjectId',
  },
  {
    title: '辅助核算',
    field: 'auxiliary',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '凭证ID',
    fieldName: 'voucherId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '会计科目',
    fieldName: 'subject',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '备注',
    fieldName: 'comment',
    component: 'Textarea',
  },
  {
    label: '贷方金额',
    fieldName: 'credit',
    component: 'Input',
  },
  {
    label: '借方金额',
    fieldName: 'debit',
    component: 'Input',
  },
  {
    label: '摘要',
    fieldName: 'summary',
    component: 'Textarea',
  },
  {
    label: '排序索引',
    fieldName: 'idx',
    component: 'Input',
  },
  {
    label: '科目id',
    fieldName: 'subjectId',
    component: 'Input',
  },
  {
    label: '辅助核算',
    fieldName: 'auxiliary',
    component: 'Input',
  },
];

import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'invoiceId',
    label: '发票ID',
  },
  {
    component: 'Input',
    fieldName: 'costName',
    label: '货物或应税劳务名称',
  },
  {
    component: 'Input',
    fieldName: 'specificationModel',
    label: '规格型号',
  },
  {
    component: 'Input',
    fieldName: 'unit',
    label: '单位',
  },
  {
    component: 'Input',
    fieldName: 'count',
    label: '数量',
  },
  {
    component: 'Input',
    fieldName: 'unitPrice',
    label: '单价',
  },
  {
    component: 'Input',
    fieldName: 'amount',
    label: '金额',
  },
  {
    component: 'Input',
    fieldName: 'taxRate',
    label: '税率',
  },
  {
    component: 'Input',
    fieldName: 'taxAmount',
    label: '税额',
  },
  {
    component: 'Input',
    fieldName: 'totalPrice',
    label: '价税合计',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
    fieldName: 'status',
    label: '状态',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '发票明细ID',
    field: 'invoiceDetailId',
  },
  {
    title: '发票ID',
    field: 'invoiceId',
  },
  {
    title: '货物或应税劳务名称',
    field: 'costName',
  },
  {
    title: '规格型号',
    field: 'specificationModel',
  },
  {
    title: '单位',
    field: 'unit',
  },
  {
    title: '数量',
    field: 'count',
  },
  {
    title: '单价',
    field: 'unitPrice',
  },
  {
    title: '金额',
    field: 'amount',
  },
  {
    title: '税率',
    field: 'taxRate',
  },
  {
    title: '税额',
    field: 'taxAmount',
  },
  {
    title: '价税合计',
    field: 'totalPrice',
  },
  {
    title: '状态',
    field: 'status',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '发票明细ID',
    fieldName: 'invoiceDetailId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '发票ID',
    fieldName: 'invoiceId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '货物或应税劳务名称',
    fieldName: 'costName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '规格型号',
    fieldName: 'specificationModel',
    component: 'Input',
  },
  {
    label: '单位',
    fieldName: 'unit',
    component: 'Input',
  },
  {
    label: '数量',
    fieldName: 'count',
    component: 'Input',
  },
  {
    label: '单价',
    fieldName: 'unitPrice',
    component: 'Input',
  },
  {
    label: '金额',
    fieldName: 'amount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '税率',
    fieldName: 'taxRate',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '税额',
    fieldName: 'taxAmount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '价税合计',
    fieldName: 'totalPrice',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
    rules: 'selectRequired',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Input',
  },
];

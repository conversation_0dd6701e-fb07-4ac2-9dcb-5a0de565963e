import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'scene',
    label: '场景',
  },
  {
    component: 'Input',
    fieldName: 'description',
    label: '描述',
  },
  {
    component: 'Textarea',
    fieldName: 'voucherEntry',
    label: '凭证分录',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: 'ID',
    field: 'id',
  },
  {
    title: '场景',
    field: 'scene',
  },
  {
    title: '描述',
    field: 'description',
  },
  {
    title: '凭证分录',
    field: 'voucherEntry',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: 'ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '场景',
    fieldName: 'scene',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '描述',
    fieldName: 'description',
    component: 'Input',
  },
  {
    label: '凭证分录',
    fieldName: 'voucherEntry',
    component: 'Textarea',
  },
];

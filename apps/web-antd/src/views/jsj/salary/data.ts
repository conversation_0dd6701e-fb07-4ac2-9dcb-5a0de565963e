import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerId',
    label: '客户ID',
  },
  {
    component: 'Input',
    fieldName: 'year',
    label: '工资所属年份',
  },
  {
    component: 'Input',
    fieldName: 'month',
    label: '工资所属月份',
  },
  {
    component: 'Input',
    fieldName: 'employeeId',
    label: '员工ID',
  },
  {
    component: 'Input',
    fieldName: 'basicWage',
    label: '基本工资',
  },
  {
    component: 'Input',
    fieldName: 'attendanceDays',
    label: '出勤天数',
  },
  {
    component: 'Input',
    fieldName: 'attendanceSalary',
    label: '出勤工资',
  },
  {
    component: 'Input',
    fieldName: 'bonus',
    label: '奖金',
  },
  {
    component: 'Input',
    fieldName: 'allowance',
    label: '津贴',
  },
  {
    component: 'Input',
    fieldName: 'subsidies',
    label: '补贴',
  },
  {
    component: 'Input',
    fieldName: 'deductions',
    label: '扣除项',
  },
  {
    component: 'Input',
    fieldName: 'income',
    label: '应发工资',
  },
  {
    component: 'Input',
    fieldName: 'dkEndowmentInsurance',
    label: '养老保险个人缴纳部分',
  },
  {
    component: 'Input',
    fieldName: 'dkMedicalInsurance',
    label: '医疗保险个人缴纳部分',
  },
  {
    component: 'Input',
    fieldName: 'dkUnemploymentInsurance',
    label: '失业保险个人缴纳部分',
  },
  {
    component: 'Input',
    fieldName: 'dkHouseProvidentFund',
    label: '公积金个人缴纳部分',
  },
  {
    component: 'Input',
    fieldName: 'dkCompany',
    label: '公司代扣款项',
  },
  {
    component: 'Input',
    fieldName: 'dkOther',
    label: '其他代扣项',
  },
  {
    component: 'Input',
    fieldName: 'realWage',
    label: '实发工资',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '工资所属年份',
    field: 'year',
  },
  {
    title: '工资所属月份',
    field: 'month',
  },
  {
    title: '员工ID',
    field: 'employeeId',
  },
  {
    title: '基本工资',
    field: 'basicWage',
  },
  {
    title: '出勤天数',
    field: 'attendanceDays',
  },
  {
    title: '出勤工资',
    field: 'attendanceSalary',
  },
  {
    title: '奖金',
    field: 'bonus',
  },
  {
    title: '津贴',
    field: 'allowance',
  },
  {
    title: '补贴',
    field: 'subsidies',
  },
  {
    title: '扣除项',
    field: 'deductions',
  },
  {
    title: '应发工资',
    field: 'income',
  },
  {
    title: '养老保险个人缴纳部分',
    field: 'dkEndowmentInsurance',
  },
  {
    title: '医疗保险个人缴纳部分',
    field: 'dkMedicalInsurance',
  },
  {
    title: '失业保险个人缴纳部分',
    field: 'dkUnemploymentInsurance',
  },
  {
    title: '公积金个人缴纳部分',
    field: 'dkHouseProvidentFund',
  },
  {
    title: '公司代扣款项',
    field: 'dkCompany',
  },
  {
    title: '其他代扣项',
    field: 'dkOther',
  },
  {
    title: '实发工资',
    field: 'realWage',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '工资所属年份',
    fieldName: 'year',
    component: 'Input',
  },
  {
    label: '工资所属月份',
    fieldName: 'month',
    component: 'Input',
  },
  {
    label: '员工ID',
    fieldName: 'employeeId',
    component: 'Input',
  },
  {
    label: '基本工资',
    fieldName: 'basicWage',
    component: 'Input',
  },
  {
    label: '出勤天数',
    fieldName: 'attendanceDays',
    component: 'Input',
  },
  {
    label: '出勤工资',
    fieldName: 'attendanceSalary',
    component: 'Input',
  },
  {
    label: '奖金',
    fieldName: 'bonus',
    component: 'Input',
  },
  {
    label: '津贴',
    fieldName: 'allowance',
    component: 'Input',
  },
  {
    label: '补贴',
    fieldName: 'subsidies',
    component: 'Input',
  },
  {
    label: '扣除项',
    fieldName: 'deductions',
    component: 'Input',
  },
  {
    label: '应发工资',
    fieldName: 'income',
    component: 'Input',
  },
  {
    label: '养老保险个人缴纳部分',
    fieldName: 'dkEndowmentInsurance',
    component: 'Input',
  },
  {
    label: '医疗保险个人缴纳部分',
    fieldName: 'dkMedicalInsurance',
    component: 'Input',
  },
  {
    label: '失业保险个人缴纳部分',
    fieldName: 'dkUnemploymentInsurance',
    component: 'Input',
  },
  {
    label: '公积金个人缴纳部分',
    fieldName: 'dkHouseProvidentFund',
    component: 'Input',
  },
  {
    label: '公司代扣款项',
    fieldName: 'dkCompany',
    component: 'Input',
  },
  {
    label: '其他代扣项',
    fieldName: 'dkOther',
    component: 'Input',
  },
  {
    label: '实发工资',
    fieldName: 'realWage',
    component: 'Input',
  },
];

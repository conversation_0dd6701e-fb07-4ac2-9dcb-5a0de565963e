<script lang="ts" setup>
  import type {
    CompaniesQueryParams,
    CompanyData,
  } from '#/api/original-voucher/types';

  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import { SearchOutlined } from '@ant-design/icons-vue';
  import {
    Empty,
    Input,
    Select,
    Switch,
    Table,
    Tag,
    Tooltip,
  } from 'ant-design-vue';
  import dayjs from 'dayjs';

  import {
    fetchCompaniesList,
    updateAutoMode,
  } from '#/api/original-voucher/api-v2';
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';

  const userStore = useUserStore();
  const router = useRouter();
  const { fetchCompanyNames, selectedCompany, selectedMonth } =
    useCompanySelection();
  const loading = ref(false);
  const companies = ref<CompanyData[]>([]);
  const searchKeyword = ref('');
  const statusFilter = ref<string>('all');

  // 状态筛选选项
  const statusOptions = [
    { label: '全部', value: 'all' },
    { label: '已置顶', value: 'top' },
    { label: '销项发票已完成', value: 'output_invoice_done' },
    { label: '进项普票已完成', value: 'input_general_done' },
    { label: '进项专票已完成', value: 'input_vat_done' },
    { label: '银行回单已完成', value: 'bank_receipt_done' },
    { label: 'AI自动模式', value: 'auto_mode_on' },
  ];

  // 过滤后的公司列表（带预处理的工具提示）
  const filteredCompanies = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return [];
    }

    let result = companies.value;

    // 关键词搜索 - 同时搜索客户名称和客户编号
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(
        (company) =>
          company.customerName.toLowerCase().includes(keyword) ||
          (company.customerCode &&
            company.customerCode.toLowerCase().includes(keyword)),
      );
    }

    // 状态筛选
    if (statusFilter.value !== 'all') {
      switch (statusFilter.value) {
        case 'auto_mode_on': {
          result = result.filter(
            (company) => company.configs?.auto_mode === 'autojob',
          );
          break;
        }
        case 'bank_receipt_done': {
          result = result.filter(
            (company) => company.bank_receipt?.status === '3',
          );
          break;
        }
        case 'input_general_done': {
          result = result.filter(
            (company) => company.input_invoice_general?.status === '3',
          );
          break;
        }
        case 'input_vat_done': {
          result = result.filter(
            (company) => company.input_invoice_vat?.status === '3',
          );
          break;
        }
        case 'output_invoice_done': {
          result = result.filter(
            (company) => company.output_invoice?.status === '3',
          );
          break;
        }
        case 'top': {
          result = result.filter((company) => company.topFlag);
          break;
        }
      }
    }

    // 预处理工具提示内容，避免在渲染时计算
    return result.map((company) => ({
      ...company,
      _tooltips: {
        bank_receipt: getStatusTooltip(company.bank_receipt?.status || '0'),
        customs_declaration_form: getStatusTooltip(
          company.customs_declaration_form?.status || '0',
        ),
        input_invoice_general: getStatusTooltip(
          company.input_invoice_general?.status || '0',
        ),
        input_invoice_vat: getStatusTooltip(
          company.input_invoice_vat?.status || '0',
        ),
        output_invoice: getStatusTooltip(company.output_invoice?.status || '0'),
        payroll: getStatusTooltip(company.payroll?.status || '0'),
      },
    }));
  });

  // 表格列定义
  const columns = [
    {
      dataIndex: 'customerName',
      ellipsis: true,
      key: 'customerName',
      title: '公司名称',
      width: 200,
    },
    {
      align: 'center' as const,
      key: 'output_invoice',
      title: '销项发票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_general',
      title: '进项普票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_vat',
      title: '进项专票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'bank_receipt',
      title: '银行回单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'payroll',
      title: '薪酬',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'customs_declaration_form',
      title: '报关单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'auto_mode',
      title: 'AI自动',
      width: 100,
    },
    {
      align: 'center' as const,
      key: 'action',
      title: '操作',
      width: 120,
    },
  ];

  // 预定义所有状态的工具提示内容（避免运行时计算）
  const STATUS_TOOLTIPS = {
    '3': '✅ 凭证已回写\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n✓ 凭证已回写\n\n进度: 3/3 (100%)',
    '2': '🔄 凭证已生成\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n○ 待回写凭证\n\n进度: 2/3 (67%)',
    '1': '📊 数据已同步\n━━━━━━━━━━━━━━\n✓ 数据已同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 1/3 (33%)',
    '0': '❌ 数据未同步\n━━━━━━━━━━━━━━\n✗ 数据未同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 0/3 (0%)',
  } as const;

  // 获取状态工具提示信息（直接查表，无计算）
  const getStatusTooltip = (status: string) => {
    return (
      STATUS_TOOLTIPS[status as keyof typeof STATUS_TOOLTIPS] ||
      `状态: ${status || '未知'}\n请检查数据状态`
    );
  };

  // 统计数据
  const statistics = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return {
        bankReceiptDone: 0,
        inputCommonDone: 0,
        inputVatDone: 0,
        outputVoiceDone: 0,
        topCount: 0,
        total: 0,
      };
    }

    const total = companies.value.length;
    const topCount = companies.value.filter((c) => c.topFlag).length;

    // 根据新的数据结构计算统计
    const outputVoiceDone = companies.value.filter(
      (c) => c.output_invoice?.status === '3',
    ).length;
    const inputCommonDone = companies.value.filter(
      (c) => c.input_invoice_general?.status === '3',
    ).length;
    const inputVatDone = companies.value.filter(
      (c) => c.input_invoice_vat?.status === '3',
    ).length;
    const bankReceiptDone = companies.value.filter(
      (c) => c.bank_receipt?.status === '3',
    ).length;

    return {
      bankReceiptDone,
      inputCommonDone,
      inputVatDone,
      outputVoiceDone,
      topCount,
      total,
    };
  });

  // 月份选择选项（使用与useCompanySelection相同的逻辑）
  const monthOptions = computed(() => {
    const options = [];
    const currentDate = dayjs();

    for (let i = 0; i < 12; i++) {
      const date = currentDate.subtract(i, 'month');
      options.push({
        label: date.format('YYYY年MM月'),
        value: date.format('YYYYMM'),
      });
    }

    return options;
  });

  // 获取公司列表数据
  async function fetchCompanies() {
    try {
      loading.value = true;
      const username = userStore.userInfo?.username;
      if (!username) {
        console.error('用户名未找到');
        return;
      }

      const params: CompaniesQueryParams = {
        account_name: username,
        month: selectedMonth.value,
        refresh: 0,
      };

      const response = await fetchCompaniesList(params);
      console.log('response', response);

      if (response.success) {
        companies.value = response.data;
      } else {
        console.error('获取公司列表失败:', response.message);
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 月份变化时重新获取数据
  const handleMonthChange = () => {
    fetchCompanies();
  };

  // 处理AI自动模式切换
  const handleAutoModeChange = async (
    record: CompanyData,
    checked: boolean,
  ) => {
    try {
      console.log(
        '切换AI自动模式:',
        record.customerName,
        checked ? '开启' : '关闭',
      );

      // 调用API更新AI自动模式
      const response = await updateAutoMode({
        auto_mode: checked ? 'autojob' : 'close',
        company_name: record.customerName,
      });

      if (response.success) {
        // 更新本地数据
        const company = companies.value.find(
          (c) => c.customerName === record.customerName,
        );
        if (company && company.configs) {
          company.configs.auto_mode = checked ? 'autojob' : 'close';
        }
        console.log('AI自动模式更新成功');
      } else {
        console.error('更新AI自动模式失败:', response.message);
      }
    } catch (error) {
      console.error('更新AI自动模式失败:', error);
      // 这里可以添加错误提示
    }
  };

  // 处理AI工作台按钮点击
  const handleAIWorkspace = (record: CompanyData) => {
    console.log('打开AI凭证页面:', record.customerName);

    // 设置当前选择的公司到全局状态
    selectedCompany.value = record.customerName;
    console.log('已设置全局选中公司:', record.customerName);

    // 跳转到AI凭证页面，传递公司名称作为参数
    router.push({
      path: '/bookkeeping/view',
      query: {
        company: record.customerName,
        month: selectedMonth.value,
      },
    });
  };

  function getProgressInfo(status: string) {
    switch (status) {
      case '3': {
        return { color: 'text-green-500', current: 3, stroke: '#22c55e' };
      }
      case '2': {
        return { color: 'text-blue-500', current: 2, stroke: '#3b82f6' };
      }
      case '1': {
        return { color: 'text-blue-500', current: 1, stroke: '#3b82f6' };
      }
      case '0': {
        return { color: 'text-red-500', current: 0, stroke: '#ef4444' };
      }
      default: {
        return { color: 'text-gray-400', current: 0, stroke: '#a3a3a3' };
      }
    }
  }

  // 监听全局月份状态变化
  watch(
    () => selectedMonth.value,
    () => {
      fetchCompanies();
    },
  );

  onMounted(async () => {
    // 初始化公司列表
    await fetchCompanyNames();
    // 获取公司数据
    fetchCompanies();
  });
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <!-- 状态图例说明 -->
    <div class="mb-4 rounded-lg bg-white px-4 py-3 shadow-sm">
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-gray-700">进度状态说明</span>
        <div class="flex items-center space-x-6">
          <!-- 状态图例 -->
          <div class="flex items-center space-x-1">
            <div class="h-3 w-3 rounded-full bg-gray-400"></div>
            <span class="text-xs text-gray-600">无业务</span>
          </div>
          <div class="flex items-center space-x-1">
            <div class="h-3 w-3 rounded-full bg-red-500"></div>
            <span class="text-xs text-gray-600">数据未同步</span>
          </div>
          <div class="flex items-center space-x-1">
            <div class="h-3 w-3 rounded-full bg-blue-500"></div>
            <span class="text-xs text-gray-600">进行中</span>
          </div>
          <div class="flex items-center space-x-1">
            <div class="h-3 w-3 rounded-full bg-green-500"></div>
            <span class="text-xs text-gray-600">凭证已回写</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 控制面板 -->
    <div class="mb-6 rounded-lg bg-white px-4 py-4 shadow-sm">
      <!-- 页面标题行 -->
      <div class="mb-4 flex items-center space-x-4">
        <span class="text-sm text-orange-500">进度统计</span>
        <span class="text-gray-400">|</span>
        <span class="text-sm text-gray-600">账套: {{ statistics.total }}</span>
      </div>

      <!-- 控制区域 -->
      <div class="flex flex-wrap items-center gap-4">
        <!-- 月份选择 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">月份:</span>
          <Select
            v-model:value="selectedMonth"
            placeholder="选择月份"
            size="middle"
            :options="monthOptions"
            class="w-32"
            @change="handleMonthChange"
          />
        </div>

        <!-- 搜索和筛选 -->
        <div class="flex flex-col gap-4 md:flex-row">
          <div class="md:w-80">
            <Input
              v-model:value="searchKeyword"
              placeholder="请输入客户名称或编号"
              size="middle"
              allow-clear
              class="text-sm"
            >
              <template #prefix>
                <SearchOutlined class="text-sm text-gray-400" />
              </template>
            </Input>
          </div>
          <div class="w-full md:w-48">
            <Select
              v-model:value="statusFilter"
              placeholder="筛选状态"
              size="middle"
              :options="statusOptions"
              class="w-full text-sm"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 公司列表表格 -->
    <div class="rounded-lg bg-white shadow-sm">
      <Table
        :columns="columns"
        :data-source="filteredCompanies"
        :loading="loading"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100'],
          defaultPageSize: 20,
        }"
        row-key="customerName"
        size="middle"
      >
        <!-- 表格单元格渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 公司名称列 -->
          <template v-if="column.key === 'customerName'">
            <div class="flex items-center">
              <Tag v-if="record.topFlag" color="orange" class="mr-2">置顶</Tag>
              <span :title="record.customerName">
                {{ record.customerName }}
              </span>
            </div>
          </template>

          <!-- 业务数据状态列 -->
          <template
            v-else-if="
              [
                'output_invoice',
                'input_invoice_general',
                'input_invoice_vat',
                'bank_receipt',
                'payroll',
                'customs_declaration_form',
              ].includes(column.key as string)
            "
          >
            <div class="flex justify-center">
              <Tooltip placement="top" trigger="hover">
                <template #title>
                  <div
                    class="tooltip-content"
                    v-html="
                      record._tooltips[column.key as string].replace(
                        /\n/g,
                        '<br>',
                      )
                    "
                  ></div>
                </template>
                <span class="inline-block cursor-pointer">
                  <svg class="h-8 w-8" viewBox="0 0 32 32">
                    <!-- 背景圆环 -->
                    <circle
                      cx="16"
                      cy="16"
                      r="14"
                      stroke="#e5e7eb"
                      stroke-width="3"
                      fill="none"
                    />
                    <!-- 进度圆环 -->
                    <circle
                      v-if="
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).current > 0
                      "
                      cx="16"
                      cy="16"
                      r="14"
                      :stroke="
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).stroke
                      "
                      stroke-width="3"
                      fill="none"
                      stroke-linecap="round"
                      :stroke-dasharray="`${(getProgressInfo(record[column.key as string]?.status || '0').current / 3) * 87.96}, 87.96`"
                      stroke-dashoffset="0"
                    />
                    <!-- 中心数字 -->
                    <text
                      x="16"
                      y="21"
                      text-anchor="middle"
                      font-size="13"
                      :fill="
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).stroke
                      "
                    >
                      {{
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).current
                      }}/3
                    </text>
                  </svg>
                </span>
              </Tooltip>
            </div>
          </template>

          <!-- AI自动模式列 -->
          <template v-else-if="column.key === 'auto_mode'">
            <div class="flex justify-center">
              <Switch
                :checked="record.configs?.auto_mode === 'autojob'"
                size="small"
                @change="
                  (checked) =>
                    handleAutoModeChange(record as CompanyData, !!checked)
                "
              />
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <div class="flex justify-center space-x-2">
              <button
                class="rounded bg-blue-500 px-3 py-1 text-xs text-white transition-colors hover:bg-blue-600"
                @click="handleAIWorkspace(record as CompanyData)"
              >
                工作台
              </button>
            </div>
          </template>
        </template>

        <!-- 空数据 -->
        <template #emptyText>
          <Empty description="暂无公司数据" />
        </template>
      </Table>
    </div>
  </div>
</template>

<style scoped>
  /* 优化 Tooltip 样式 */
  :deep(.ant-tooltip) {
    z-index: 1000;
    transition:
      opacity 0.2s ease-in-out,
      transform 0.2s ease-in-out;
  }

  :deep(.ant-tooltip-inner) {
    position: relative;
    max-width: 280px;
    padding: 16px 20px;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.6;
    color: #f9fafb;
    letter-spacing: 0.025em;
    background: linear-gradient(135deg, #1f2937 0%, #**********%);
    border: 1px solid #4b5563;
    border-radius: 10px;
    box-shadow:
      0 10px 25px -5px rgb(0 0 0 / 10%),
      0 10px 10px -5px rgb(0 0 0 / 4%);
  }

  /* 自定义 Tooltip 内容样式 */
  :deep(.tooltip-content) {
    word-break: break-word;
    white-space: normal;
  }

  :deep(.tooltip-content br) {
    display: block;
    margin: 4px 0;
  }

  :deep(.ant-tooltip-arrow::before) {
    background-color: #1f2937;
  }

  /* 确保 SVG 元素能正确接收鼠标事件 */
  :deep(svg) {
    pointer-events: auto;
  }
</style>

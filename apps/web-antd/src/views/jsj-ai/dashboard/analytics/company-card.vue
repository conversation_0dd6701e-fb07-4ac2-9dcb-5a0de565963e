<script setup lang="ts">
  import type { CompanyData } from '#/api/original-voucher/types';

  interface Props {
    company: CompanyData;
  }

  defineProps<Props>();

  // 获取状态样式类
  function getStatusClass(status: string) {
    return status === 'DONE'
      ? 'px-2 py-1 text-xs rounded-full bg-green-100 text-green-800'
      : 'px-2 py-1 text-xs rounded-full bg-red-100 text-red-800';
  }

  // 获取记账状态样式类
  function getAccountStatusClass(status: string) {
    if (status === 'DONE') {
      return 'px-2 py-1 text-xs rounded-full bg-green-100 text-green-800';
    } else if (status === 'NO') {
      return 'px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800';
    } else {
      return 'px-2 py-1 text-xs rounded-full bg-red-100 text-red-800';
    }
  }

  // 获取状态文本
  function getStatusText(status: string) {
    return status === 'DONE' ? '已完成' : '未完成';
  }

  // 获取记账状态文本
  function getAccountStatusText(status: string) {
    if (status === 'DONE') return '已完成';
    if (status === 'NO') return '无需记账';
    return '未完成';
  }
</script>

<template>
  <div
    class="company-card rounded-lg bg-white p-6 shadow-md transition-shadow duration-300 hover:shadow-lg"
  >
    <!-- 公司名称和置顶标识 -->
    <div class="mb-4 flex items-center justify-between">
      <h3
        class="truncate text-lg font-semibold text-gray-800"
        :title="company.customerName"
      >
        {{ company.customerName }}
      </h3>
      <div v-if="company.topFlag" class="flex items-center">
        <svg
          class="h-4 w-4 text-yellow-500"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
          />
        </svg>
        <span class="ml-1 text-xs text-yellow-600">置顶</span>
      </div>
    </div>

    <!-- 财务状态网格 -->
    <div class="mb-4 grid grid-cols-2 gap-3">
      <!-- 发票整理状态 -->
      <div class="status-item">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">发票整理</span>
          <span :class="getStatusClass(company.invoiceArrangeStatus)">
            {{ getStatusText(company.invoiceArrangeStatus) }}
          </span>
        </div>
      </div>

      <!-- 记账状态 -->
      <div class="status-item">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">记账状态</span>
          <span :class="getAccountStatusClass(company.accountStatus)">
            {{ getAccountStatusText(company.accountStatus) }}
          </span>
        </div>
      </div>

      <!-- 税务申报状态 -->
      <div class="status-item">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">税务申报</span>
          <span :class="getStatusClass(company.taxDeclareStatus)">
            {{ getStatusText(company.taxDeclareStatus) }}
          </span>
        </div>
      </div>

      <!-- 付款状态 -->
      <div class="status-item">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">付款状态</span>
          <span :class="getStatusClass(company.paymentStatus)">
            {{ getStatusText(company.paymentStatus) }}
          </span>
        </div>
      </div>

      <!-- 清卡状态 -->
      <div class="status-item">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">清卡状态</span>
          <span :class="getStatusClass(company.clearCardStatus)">
            {{ getStatusText(company.clearCardStatus) }}
          </span>
        </div>
      </div>

      <!-- 抄税状态 -->
      <div class="status-item">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">抄税状态</span>
          <span :class="getStatusClass(company.copyTaxStatus)">
            {{ getStatusText(company.copyTaxStatus) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 记账详细状态进度条 -->
    <div class="mb-3">
      <div class="mb-1 flex items-center justify-between">
        <span class="text-sm text-gray-600">记账进度</span>
        <span class="text-xs text-gray-500">
          {{ company.accountStatusDetail }}/4
        </span>
      </div>
      <div class="h-2 w-full rounded-full bg-gray-200">
        <div
          class="h-2 rounded-full bg-blue-500 transition-all duration-300"
          :style="{ width: `${(company.accountStatusDetail / 4) * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- 最新备注 -->
    <div
      v-if="company.latestNote"
      class="mt-3 rounded bg-gray-50 p-2 text-sm text-gray-600"
    >
      <span class="font-medium">备注：</span>
      {{ company.latestNote }}
    </div>
  </div>
</template>

<style scoped>
  .company-card {
    border: 1px solid #e5e7eb;
  }

  .status-item {
    padding: 8px;
    background-color: #f9fafb;
    border-radius: 6px;
  }

  .company-card:hover {
    transform: translateY(-2px);
  }
</style>

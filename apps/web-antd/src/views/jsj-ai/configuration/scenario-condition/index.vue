<script setup lang="ts">
  import type { TableColumnsType } from 'ant-design-vue';

  import { computed, h, onMounted, reactive, ref, watch } from 'vue';

  import { useVbenModal } from '@vben/common-ui';

  import { message } from 'ant-design-vue';

  import {
    deleteScenarioCondition,
    fetchScenarioConditionList,
    fetchScenarioEntryList,
  } from '#/api/original-voucher/api-v2';
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';
  import { useSceneOptions } from '#/hooks/useSceneOptions';

  import scenarioConditionModal from './scenarioCondition-modal.vue';

  // 使用公司选择功能
  const { companyList, fetchCompanyNames, selectedCompany } =
    useCompanySelection();

  // 表单数据
  const searchForm = reactive({
    company_name: '',
    scene: '',
    type: '',
  });

  // 表格数据
  const allData = ref<any[]>([]); // 存储所有原始数据
  const dataSource = ref<any[]>([]); // 显示的过滤后数据
  const loading = ref(false);
  const sceneOptions = ref<{ label: string; value: string }[]>([]); // 场景选项

  // 公司选项
  const companyOptions = computed(() =>
    companyList.value.map((company) => ({
      label: company.name,
      value: company.name,
    })),
  );

  // 使用场景选择hook
  const companyNameRef = {
    value: computed(() => searchForm.company_name),
  };
  const { loading: loadingScenes, sceneOptions: sceneOptionsList } =
    useSceneOptions(companyNameRef);

  // 更新场景选项
  watch(
    sceneOptionsList,
    (newOptions) => {
      sceneOptions.value = newOptions;
    },
    { deep: true },
  );

  // 获取场景选项
  async function fetchSceneOptions() {
    try {
      if (!searchForm.company_name) return;
      const params = {
        company_name: searchForm.company_name,
        needDefault: 1 as 1,
        status: 1 as 1,
      };
      const result = await fetchScenarioEntryList(params);
      const scenes = result?.data || [];
      // 提取唯一的场景名称
      const uniqueScenes = [
        ...new Set(scenes.map((item) => item.scene)),
      ].filter(Boolean);
      sceneOptions.value = uniqueScenes.map((scene) => ({
        label: scene,
        value: scene,
      }));
    } catch (error) {
      console.error('获取场景选项失败:', error);
      message.error('获取场景选项失败');
      sceneOptions.value = [];
    }
  }

  // 监听公司名称变化，重新获取场景选项
  watch(
    () => searchForm.company_name,
    () => {
      fetchSceneOptions();
    },
  );

  // 前端搜索过滤函数
  function filterData() {
    let filtered = [...allData.value];

    // 按场景过滤
    if (searchForm.scene.trim()) {
      filtered = filtered.filter((item) =>
        item.scene?.toLowerCase().includes(searchForm.scene.toLowerCase()),
      );
    }

    // 按类型过滤
    if (searchForm.type.trim()) {
      filtered = filtered.filter((item) =>
        item.type?.toLowerCase().includes(searchForm.type.toLowerCase()),
      );
    }

    dataSource.value = filtered;
    console.log('前端过滤结果:', filtered.length, '条');
  }

  // 表格列定义
  const columns: TableColumnsType = [
    {
      dataIndex: 'company_name',
      ellipsis: true,
      key: 'company_name',
      title: '公司名称',
      width: 200,
      minWidth: 200,
      fixed: false,
    },
    {
      dataIndex: 'scene',
      ellipsis: true,
      key: 'scene',
      title: '场景',
      width: 180,
      minWidth: 180,
      fixed: false,
    },
    {
      dataIndex: 'type',
      ellipsis: true,
      key: 'type',
      title: '类型',
      width: 120,
      minWidth: 120,
      responsive: ['md'],
    },
    {
      customRender: ({ record }) => {
        if (!record.condition || !Array.isArray(record.condition)) {
          return h('span', { class: 'text-gray-400' }, '无条件');
        }

        const conditions = record.condition;
        const isSingleCondition = conditions.length === 1;

        if (isSingleCondition) {
          // 单条条件，横向展示
          const item = conditions[0];
          return h('div', { class: 'condition-detail-single' }, [
            h('div', { class: 'condition-item' }, [
              h('span', { class: 'condition-label' }, `${item.name}:`),
              h(
                'span',
                { class: 'condition-value condition-badge' },
                item.value || '-',
              ),
            ]),
          ]);
        } else {
          // 多条条件，纵向展示
          return h(
            'div',
            { class: 'condition-detail-multiple' },
            conditions.map((item: any, index: number) =>
              h(
                'div',
                {
                  class: `condition-row ${index % 2 === 0 ? 'condition-row-even' : 'condition-row-odd'}`,
                  key: index,
                },
                [
                  // h('div', { class: 'condition-index' }, `${index + 1}.`),
                  h('div', { class: 'condition-content' }, [
                    h('span', { class: 'condition-name' }, `${item.name}:`),
                    h(
                      'span',
                      { class: 'condition-value condition-badge' },
                      item.value || '-',
                    ),
                  ]),
                ],
              ),
            ),
          );
        }
      },
      dataIndex: 'condition',
      key: 'condition',
      title: '条件配置',
      width: 300,
      minWidth: 250,
      responsive: ['lg'],
    },
    {
      customRender: ({ text }) => {
        if (!text) return '';
        return new Date(text).toLocaleString('zh-CN');
      },
      dataIndex: 'created_at',
      key: 'created_at',
      title: '创建时间',
      width: 160,
      responsive: ['xl'],
    },
    {
      customRender: ({ text }) => {
        if (!text) return '';
        return new Date(text).toLocaleString('zh-CN');
      },
      dataIndex: 'updated_at',
      key: 'updated_at',
      title: '更新时间',
      width: 160,
      responsive: ['xxl'],
    },
    {
      fixed: 'right',
      key: 'action',
      title: '操作',
      width: 180,
    },
  ];

  const [ScenarioConditionModal, modalApi] = useVbenModal({
    connectedComponent: scenarioConditionModal,
  });

  // 查询数据（只按公司过滤，获取所有数据）
  async function fetchData() {
    try {
      loading.value = true;
      const params: any = {
        company_name: searchForm.company_name || '',
      };

      const result = await fetchScenarioConditionList(params);
      allData.value = result?.data || [];
      console.log('查询场景条件数据:', allData.value.length, '条');

      // 获取数据后显示所有数据，不自动过滤
      dataSource.value = [...allData.value];
    } catch (error) {
      console.error('查询场景条件列表失败:', error);
      message.error('查询失败，请重试');
      allData.value = [];
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 处理公司选择变化（需要重新获取数据）
  function handleCompanyChange(value: any) {
    const companyName = String(value || '');
    searchForm.company_name = companyName;
    searchForm.scene = ''; // 清空场景选择
    fetchData();
  }

  // 处理场景选择变化
  function handleSceneChange(value: any) {
    searchForm.scene = String(value || '');
    filterData();
  }

  // 处理搜索
  async function handleSearch() {
    loading.value = true;
    try {
      await fetchData();
    } finally {
      loading.value = false;
    }
  }

  // 处理重置
  function handleReset() {
    searchForm.company_name = '';
    searchForm.scene = '';
    searchForm.type = '';
    handleSearch();
  }

  // 设置默认选中的公司
  function setDefaultSelectedCompany() {
    const currentSelected = selectedCompany.value;
    if (currentSelected) {
      searchForm.company_name = currentSelected;
      console.log('设置默认选中公司:', currentSelected);
    }
  }

  function handleAdd() {
    modalApi.setData({});
    modalApi.open();
  }

  function handleEdit(record: any) {
    const { company_name } = searchForm;
    modalApi.setData({ company_name, record });
    modalApi.open();
  }

  async function handleDelete(row: any) {
    try {
      if (!row._id) {
        message.error('删除失败：缺少记录ID');
        return;
      }

      const result = await deleteScenarioCondition({ id: row._id });

      if (result.success) {
        message.success('删除成功');
        // 重新获取数据
        await fetchData();
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error) {
      console.error('删除场景条件失败:', error);
      message.error('删除失败，请重试');
    }
  }

  // 初始化数据
  onMounted(async () => {
    await fetchCompanyNames();
    setDefaultSelectedCompany();
    await fetchData();
  });

  // 监听全局选中公司变化
  watch(
    selectedCompany,
    (newCompany: string) => {
      if (newCompany && newCompany !== searchForm.company_name) {
        searchForm.company_name = newCompany;
        fetchData();
        console.log('同步全局选中公司到表单:', newCompany);
      }
    },
    { immediate: true },
  );
</script>

<template>
  <div class="p-4">
    <!-- 查询条件和操作按钮 -->
    <a-card class="mb-4">
      <div class="search-container">
        <!-- 查询表单 -->
        <a-form layout="inline" :model="searchForm" class="search-form">
          <a-form-item label="公司名称" class="form-item-company">
            <a-select
              v-model:value="searchForm.company_name"
              placeholder="请选择公司"
              allow-clear
              show-search
              class="company-select"
              :options="companyOptions"
              :filter-option="
                (input: string, option: any) =>
                  option?.label?.toLowerCase().includes(input.toLowerCase())
              "
              @change="handleCompanyChange"
            />
          </a-form-item>
          <a-form-item label="场景" class="form-item-scene">
            <a-select
              v-model:value="searchForm.scene"
              placeholder="请选择场景"
              class="scene-select"
              :options="sceneOptions"
              :loading="loadingScenes"
              show-search
              :filter-option="
                (input: string, option: any) =>
                  option?.label?.toLowerCase().includes(input.toLowerCase())
              "
              @change="handleSceneChange"
            />
          </a-form-item>
          <a-form-item label="类型" class="form-item-type">
            <a-input
              v-model:value="searchForm.type"
              placeholder="请输入类型"
              class="type-input"
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item class="form-item-actions">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </a-card>

    <!-- 数据表格 -->
    <a-card title="记账场景条件配置列表">
      <template #extra>
        <a-button type="primary" @click="handleAdd">新增条件</a-button>
      </template>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :scroll="{ x: 1400 }"
        row-key="_id"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确认删除？"
                placement="left"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 模态框 -->
    <ScenarioConditionModal @reload="fetchData" />
  </div>
</template>

<style scoped>
  @media (max-width: 1200px) {
    .search-container {
      flex-direction: column;
      align-items: stretch;
    }

    .search-form {
      min-width: auto;
    }

    .action-buttons {
      justify-content: flex-end;
      margin-top: 8px;
    }
  }

  /* 响应式布局：侧边栏收起时每行最多3个字段 */
  @media (max-width: 1400px) {
    .search-form .ant-form-item {
      margin-right: 12px;
    }

    .search-form .form-item-company .company-select {
      min-width: 240px;
      max-width: 280px;
    }

    .search-form .form-item-scene .scene-select {
      min-width: 220px;
      max-width: 280px;
    }

    .search-form .form-item-type .type-input {
      min-width: 140px;
      max-width: 160px;
    }
  }

  /* 小屏幕下的表格优化 */
  @media (max-width: 768px) {
    :deep(.ant-table-tbody > tr > td:nth-child(1)),
    :deep(.ant-table-thead > tr > th:nth-child(1)) {
      min-width: 150px !important;
    }

    :deep(.ant-table-tbody > tr > td:nth-child(2)),
    :deep(.ant-table-thead > tr > th:nth-child(2)) {
      min-width: 130px !important;
    }
  }

  /* 小屏幕下的表格优化 */
  @media (max-width: 768px) {
    :deep(.ant-table-tbody > tr > td:nth-child(1)),
    :deep(.ant-table-thead > tr > th:nth-child(1)) {
      min-width: 150px !important;
    }

    :deep(.ant-table-tbody > tr > td:nth-child(2)),
    :deep(.ant-table-thead > tr > th:nth-child(2)) {
      min-width: 130px !important;
    }
  }

  .search-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start;
    justify-content: space-between;
  }

  .search-form {
    flex: 1;
    min-width: 600px;
  }

  .action-buttons {
    display: flex;
    flex-shrink: 0;
    align-items: center;
  }

  .search-form .ant-form-item {
    margin-bottom: 8px;
  }

  /* 搜索表单字段样式 */
  .search-form .form-item-company .company-select {
    width: 100%;
    min-width: 280px;
    max-width: 350px;
  }

  .search-form .form-item-scene .scene-select {
    width: 100%;
    min-width: 280px;
    max-width: 350px;
  }

  .search-form .form-item-type .type-input {
    width: 100%;
    min-width: 160px;
    max-width: 200px;
  }



  /* 条件配置样式 */
  :deep(.condition-detail-single) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 8px;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  :deep(.condition-item) {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  :deep(.condition-label) {
    font-size: 12px;
    font-weight: 500;
    color: #666;
  }

  :deep(.condition-value) {
    font-size: 12px;
    color: #333;
  }

  /* 多条条件样式 */
  :deep(.condition-detail-multiple) {
    max-height: 200px;
    overflow-y: auto;
  }

  :deep(.condition-row) {
    display: flex;
    gap: 8px;
    padding: 6px 8px;
    margin-bottom: 4px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
  }

  :deep(.condition-row-even) {
    background-color: #fafafa;
  }

  :deep(.condition-row-odd) {
    background-color: #fff;
  }

  :deep(.condition-index) {
    min-width: 20px;
    padding-top: 2px;
    font-size: 12px;
    font-weight: 500;
    color: #999;
  }

  :deep(.condition-content) {
    display: flex;
    flex: 1;
    gap: 6px;
    align-items: center;
  }

  :deep(.condition-name) {
    min-width: 60px;
    font-size: 12px;
    font-weight: 500;
    color: #666;
  }

  /* 条件值徽章样式 */
  :deep(.condition-badge) {
    display: inline-block !important;
    min-width: 40px !important;
    padding: 2px 8px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
    color: #1890ff !important;
    text-align: center !important;
    background-color: #e6f7ff !important;
    border: 1px solid #91d5ff !important;
    border-radius: 10px !important;
  }

  /* 表格重要列样式 - 确保公司名称和场景列不被过度压缩 */
  :deep(.ant-table-tbody > tr > td:nth-child(1)),
  :deep(.ant-table-thead > tr > th:nth-child(1)) {
    min-width: 200px !important;
  }

  :deep(.ant-table-tbody > tr > td:nth-child(2)),
  :deep(.ant-table-thead > tr > th:nth-child(2)) {
    min-width: 180px !important;
  }
</style>

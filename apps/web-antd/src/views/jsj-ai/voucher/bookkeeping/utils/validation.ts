import type { ListItm } from '../../index.d';

export interface ValidationError {
  field: string;
  message: string;
  row?: number;
}

export interface ValidationResult {
  success: boolean;
  message: string;
  errors?: ValidationError[];
}

/**
 * 校验凭证数据
 * @param list 凭证数据列表
 * @param type 校验类型，template表示模板校验
 * @param borrowerAll 借方合计
 * @param lenderAll 贷方合计
 * @returns 校验结果
 */
export function validateVoucherData(
  list: ListItm[],
  type?: string,
  borrowerAll?: string,
  lenderAll?: string
): ValidationResult {
  const errors: ValidationError[] = [];
  let validRowCount = 0;

  // 遍历所有行进行校验
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (!item) continue;

    // 检查是否为有效行（至少有一个字段填写）
    const hasData = item.abstract || item.subject || item.borrower || item.lender;

    if (hasData) {
      validRowCount++;

      // 校验摘要
      const abstractText = typeof item.abstract === 'string' ? item.abstract : item.abstract?.text;
      if (!abstractText || !abstractText.trim()) {
        errors.push({
          field: '摘要',
          message: '摘要不能为空',
          row: i + 1
        });
      }

      // 校验科目
      if (!item.subject) {
        errors.push({
          field: '科目',
          message: '科目不能为空',
          row: i + 1
        });
      } else {
        // 校验辅助核算
        if (item.subject.useAssistant && item.subject.assistantType) {
          // 检查是否选择了辅助核算项
          if (!item.subject.assistantOptions || item.subject.assistantOptions.length === 0) {
            const assistantTypeMap: Record<string, string> = {
              's': '供应商',
              'c': '客户',
              'i': '存货',
              'p': '项目',
              'd': '部门',
              'e': '员工'
            };
            const typeName = assistantTypeMap[item.subject.assistantType] || '辅助核算';
            errors.push({
              field: '辅助核算',
              message: `${typeName}辅助核算不能为空`,
              row: i + 1
            });
          }
        }
      }

      // 校验金额（非模板时）
      if (type !== 'template') {
        const borrowerAmount = Number(item.borrower) || 0;
        const lenderAmount = Number(item.lender) || 0;

        if (borrowerAmount === 0 && lenderAmount === 0) {
          errors.push({
            field: '金额',
            message: '借方和贷方金额不能同时为空',
            row: i + 1
          });
        } else if (borrowerAmount > 0 && lenderAmount > 0) {
          errors.push({
            field: '金额',
            message: '借方和贷方金额不能同时填写',
            row: i + 1
          });
        } else if (borrowerAmount < 0 || lenderAmount < 0) {
          errors.push({
            field: '金额',
            message: '金额不能为负数',
            row: i + 1
          });
        }
      }
    } else if (i === 0) {
      // 第一行必须填写
      errors.push({
        field: '数据完整性',
        message: '请完成数据填写',
        row: 1
      });
    }
  }

  // 检查是否至少有两行有效数据
  if (validRowCount < 2 && type !== 'template') {
    errors.push({
      field: '数据完整性',
      message: '至少需要填写两行凭证数据'
    });
  }

  // 检查借贷平衡（非模板时）
  if (type !== 'template' && errors.length === 0 && borrowerAll !== undefined && lenderAll !== undefined) {
    const borrowerTotal = Number(borrowerAll) || 0;
    const lenderTotal = Number(lenderAll) || 0;

    if (Math.abs(borrowerTotal - lenderTotal) > 0.01) {
      errors.push({
        field: '借贷平衡',
        message: `借贷合计金额不平衡：借方合计 ${borrowerTotal}，贷方合计 ${lenderTotal}`
      });
    }

    if (borrowerTotal === 0 && lenderTotal === 0) {
      errors.push({
        field: '凭证金额',
        message: '凭证金额不能为零'
      });
    }
  }

  // 返回校验结果
  if (errors.length > 0) {
    const message = errors.map(error => {
      const prefix = error.row ? `第${error.row}行` : '';
      return `${prefix}${error.field}：${error.message}`;
    }).join('；');

    return {
      success: false,
      message,
      errors
    };
  }

  return {
    success: true,
    message: '校验通过'
  };
}

/**
 * 显示校验错误信息
 * @param errors 错误列表
 * @param messageApi 消息API
 */
export function showValidationErrors(errors: ValidationError[], messageApi: any) {
  const groupedErrors: Record<string, string[]> = {};
  
  errors.forEach(error => {
    const key = error.row ? `第${error.row}行` : '通用错误';
    if (!groupedErrors[key]) {
      groupedErrors[key] = [];
    }
    groupedErrors[key].push(`${error.field}：${error.message}`);
  });

  const errorMessages = Object.entries(groupedErrors).map(([key, messages]) => {
    return `${key}：${messages.join('，')}`;
  });

  messageApi.error(errorMessages.join('；'));
}

<script setup lang="ts">
import { ref } from 'vue';

import { message } from 'ant-design-vue';

import { validateVoucherData } from '../utils/validation';
import ValidationErrorModal from '../components/ValidationErrorModal.vue';

import type { ListItm } from '../../index.d';
import type { ValidationError } from '../utils/validation';

// 测试数据
const testCases = ref([
  {
    name: '正常凭证数据',
    data: [
      {
        abstract: '销售商品',
        subject: { id: '1', name: '银行存款', code: '1002', text: '1002 银行存款' },
        borrower: '1000',
        lender: ''
      },
      {
        abstract: '销售商品',
        subject: { id: '2', name: '主营业务收入', code: '6001', text: '6001 主营业务收入' },
        borrower: '',
        lender: '1000'
      }
    ] as ListItm[],
    borrowerAll: '1000',
    lenderAll: '1000',
    expectedValid: true
  },
  {
    name: '摘要为空',
    data: [
      {
        abstract: '',
        subject: { id: '1', name: '银行存款', code: '1002', text: '1002 银行存款' },
        borrower: '1000',
        lender: ''
      },
      {
        abstract: '销售商品',
        subject: { id: '2', name: '主营业务收入', code: '6001', text: '6001 主营业务收入' },
        borrower: '',
        lender: '1000'
      }
    ] as ListItm[],
    borrowerAll: '1000',
    lenderAll: '1000',
    expectedValid: false
  },
  {
    name: '科目为空',
    data: [
      {
        abstract: '销售商品',
        subject: undefined,
        borrower: '1000',
        lender: ''
      },
      {
        abstract: '销售商品',
        subject: { id: '2', name: '主营业务收入', code: '6001', text: '6001 主营业务收入' },
        borrower: '',
        lender: '1000'
      }
    ] as ListItm[],
    borrowerAll: '1000',
    lenderAll: '1000',
    expectedValid: false
  },
  {
    name: '借贷不平衡',
    data: [
      {
        abstract: '销售商品',
        subject: { id: '1', name: '银行存款', code: '1002', text: '1002 银行存款' },
        borrower: '1000',
        lender: ''
      },
      {
        abstract: '销售商品',
        subject: { id: '2', name: '主营业务收入', code: '6001', text: '6001 主营业务收入' },
        borrower: '',
        lender: '800'
      }
    ] as ListItm[],
    borrowerAll: '1000',
    lenderAll: '800',
    expectedValid: false
  },
  {
    name: '金额同时填写',
    data: [
      {
        abstract: '销售商品',
        subject: { id: '1', name: '银行存款', code: '1002', text: '1002 银行存款' },
        borrower: '1000',
        lender: '500'
      },
      {
        abstract: '销售商品',
        subject: { id: '2', name: '主营业务收入', code: '6001', text: '6001 主营业务收入' },
        borrower: '',
        lender: '1000'
      }
    ] as ListItm[],
    borrowerAll: '1000',
    lenderAll: '1500',
    expectedValid: false
  },
  {
    name: '辅助核算缺失',
    data: [
      {
        abstract: '销售商品',
        subject: { 
          id: '1', 
          name: '应收账款', 
          code: '1122', 
          text: '1122 应收账款',
          useAssistant: true,
          assistantType: 'c',
          assistantOptions: []
        },
        borrower: '1000',
        lender: ''
      },
      {
        abstract: '销售商品',
        subject: { id: '2', name: '主营业务收入', code: '6001', text: '6001 主营业务收入' },
        borrower: '',
        lender: '1000'
      }
    ] as ListItm[],
    borrowerAll: '1000',
    lenderAll: '1000',
    expectedValid: false
  }
]);

// 测试结果
const testResults = ref<Array<{
  name: string;
  passed: boolean;
  result: any;
  expected: boolean;
}>>([]);

// 校验错误模态框
const validationErrorVisible = ref(false);
const validationErrors = ref<ValidationError[]>([]);
const currentTestName = ref('');

// 运行单个测试
function runSingleTest(testCase: any) {
  const result = validateVoucherData(
    testCase.data,
    undefined,
    testCase.borrowerAll,
    testCase.lenderAll
  );

  const passed = result.success === testCase.expectedValid;

  testResults.value.push({
    name: testCase.name,
    passed,
    result,
    expected: testCase.expectedValid
  });

  if (!passed) {
    message.error(`测试失败: ${testCase.name}`);
  } else {
    message.success(`测试通过: ${testCase.name}`);
  }

  return { passed, result };
}

// 运行所有测试
function runAllTests() {
  testResults.value = [];
  
  let passedCount = 0;
  let totalCount = testCases.value.length;

  testCases.value.forEach(testCase => {
    const { passed } = runSingleTest(testCase);
    if (passed) passedCount++;
  });

  if (passedCount === totalCount) {
    message.success(`所有测试通过! (${passedCount}/${totalCount})`);
  } else {
    message.error(`测试完成: ${passedCount}/${totalCount} 通过`);
  }
}

// 查看详细错误
function viewErrors(result: any, testName: string) {
  if (result.errors && result.errors.length > 0) {
    validationErrors.value = result.errors;
    currentTestName.value = testName;
    validationErrorVisible.value = true;
  } else {
    message.info('该测试没有详细错误信息');
  }
}

// 关闭错误模态框
function handleCloseValidationError() {
  validationErrorVisible.value = false;
  validationErrors.value = [];
  currentTestName.value = '';
}
</script>

<template>
  <div class="validation-test-page">
    <a-card title="凭证校验功能测试" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="runAllTests">
          运行所有测试
        </a-button>
      </template>

      <!-- 测试用例列表 -->
      <div class="test-cases">
        <h3>测试用例</h3>
        <a-list :data-source="testCases" item-layout="horizontal">
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button size="small" @click="runSingleTest(item)">
                  运行测试
                </a-button>
              </template>
              <a-list-item-meta>
                <template #title>
                  <span>{{ item.name }}</span>
                  <a-tag :color="item.expectedValid ? 'green' : 'red'" class="ml-2">
                    预期: {{ item.expectedValid ? '通过' : '失败' }}
                  </a-tag>
                </template>
                <template #description>
                  <div class="test-data">
                    <p>数据行数: {{ item.data.length }}</p>
                    <p>借方合计: {{ item.borrowerAll }}，贷方合计: {{ item.lenderAll }}</p>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResults.length > 0" class="test-results mt-6">
        <h3>测试结果</h3>
        <a-table 
          :data-source="testResults" 
          :columns="[
            { title: '测试名称', dataIndex: 'name', key: 'name' },
            { title: '预期结果', dataIndex: 'expected', key: 'expected' },
            { title: '实际结果', dataIndex: 'result', key: 'result' },
            { title: '测试状态', dataIndex: 'passed', key: 'passed' },
            { title: '操作', key: 'action' }
          ]"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'expected'">
              <a-tag :color="record.expected ? 'green' : 'red'">
                {{ record.expected ? '通过' : '失败' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'result'">
              <a-tag :color="record.result.success ? 'green' : 'red'">
                {{ record.result.success ? '通过' : '失败' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'passed'">
              <a-tag :color="record.passed ? 'green' : 'red'">
                {{ record.passed ? '✓ 通过' : '✗ 失败' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button 
                v-if="!record.result.success" 
                size="small" 
                @click="viewErrors(record.result, record.name)"
              >
                查看错误
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 校验错误模态框 -->
    <ValidationErrorModal
      :visible="validationErrorVisible"
      :errors="validationErrors"
      :title="`${currentTestName} - 校验错误详情`"
      @close="handleCloseValidationError"
    />
  </div>
</template>

<style scoped lang="scss">
.validation-test-page {
  padding: 16px;

  .test-cases {
    .test-data {
      font-size: 12px;
      color: #666;
      
      p {
        margin: 2px 0;
      }
    }
  }

  .test-results {
    .ant-table {
      border: 1px solid #f0f0f0;
      border-radius: 6px;
    }
  }
}
</style>

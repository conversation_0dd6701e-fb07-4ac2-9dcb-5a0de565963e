# 凭证校验功能测试

## 概述

本目录包含凭证录入页面数据校验功能的测试文件，用于验证修复后的校验逻辑是否正确工作。

## 修复的问题

### 1. 校验逻辑错误
- **问题**: `data_validation`函数中的校验逻辑存在多个问题
- **修复**: 重构校验逻辑，使用统一的校验工具函数

### 2. 校验返回值处理错误
- **问题**: 在review页面中，校验失败时的处理逻辑有误
- **修复**: 正确处理校验结果的返回值格式

### 3. 缺少独立的凭证录入页面
- **问题**: 没有独立的保存按钮和校验调用
- **修复**: 创建了完整的凭证录入页面 `/bookkeeping/enter`

### 4. 辅助核算校验缺失
- **问题**: 没有对启用辅助核算的科目进行必填校验
- **修复**: 添加了辅助核算的必填校验规则

### 5. 错误提示不够清晰
- **问题**: 校验失败时的错误信息不够具体
- **修复**: 创建了详细的错误提示组件和机制

## 校验规则

### 基本校验
1. **摘要校验**: 摘要不能为空
2. **科目校验**: 科目不能为空
3. **金额校验**: 
   - 借方和贷方金额不能同时为空
   - 借方和贷方金额不能同时填写
   - 金额不能为负数

### 辅助核算校验
- 对于启用辅助核算的科目，必须选择对应的辅助核算项
- 支持的辅助核算类型：
  - `s`: 供应商
  - `c`: 客户
  - `i`: 存货
  - `p`: 项目
  - `d`: 部门
  - `e`: 员工

### 借贷平衡校验
- 借方合计必须等于贷方合计（允许0.01的误差）
- 凭证金额不能为零

### 数据完整性校验
- 至少需要填写两行凭证数据
- 第一行必须填写

## 测试用例

### 1. 正常凭证数据
- 所有字段正确填写
- 借贷平衡
- 预期结果：通过

### 2. 摘要为空
- 第一行摘要为空
- 预期结果：失败

### 3. 科目为空
- 第一行科目为空
- 预期结果：失败

### 4. 借贷不平衡
- 借方合计1000，贷方合计800
- 预期结果：失败

### 5. 金额同时填写
- 同一行借方和贷方都有金额
- 预期结果：失败

### 6. 辅助核算缺失
- 启用辅助核算的科目未选择辅助核算项
- 预期结果：失败

## 使用方法

### 访问测试页面
```
http://localhost:5173/bookkeeping/test/validation
```

### 运行测试
1. 点击"运行所有测试"按钮执行全部测试用例
2. 点击单个测试用例的"运行测试"按钮执行特定测试
3. 对于失败的测试，点击"查看错误"按钮查看详细错误信息

### 访问凭证录入页面
```
http://localhost:5173/bookkeeping/enter
```

## 文件结构

```
test/
├── validation-test.vue     # 校验功能测试页面
└── README.md              # 本文档

../utils/
└── validation.ts          # 校验工具函数

../components/
└── ValidationErrorModal.vue  # 错误提示模态框

../enter/
└── index.vue             # 独立的凭证录入页面
```

## 验证步骤

1. **基本校验测试**
   - 访问测试页面，运行所有测试用例
   - 确保所有测试用例都按预期通过或失败

2. **实际使用测试**
   - 访问凭证录入页面
   - 尝试保存空数据，应该显示校验错误
   - 填写不平衡的数据，应该显示借贷不平衡错误
   - 填写正确数据，应该保存成功

3. **错误提示测试**
   - 触发各种校验错误
   - 确认错误提示信息清晰明确
   - 确认错误提示模态框正常显示

## 注意事项

- 测试页面仅用于开发和测试，在生产环境中应该隐藏
- 校验逻辑已经集成到VoucherEditing组件中，其他使用该组件的页面也会受益
- 如果需要添加新的校验规则，请在`validation.ts`文件中统一添加

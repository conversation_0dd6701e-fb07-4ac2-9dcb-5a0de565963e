<script setup lang="ts">
import { computed } from 'vue';

interface ValidationError {
  field: string;
  message: string;
  row?: number;
}

interface Props {
  visible: boolean;
  errors: ValidationError[];
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '数据校验失败'
});

const emit = defineEmits<{
  close: [];
}>();

// 按行分组错误
const groupedErrors = computed(() => {
  const groups: Record<string, ValidationError[]> = {};
  
  props.errors.forEach(error => {
    const key = error.row ? `第${error.row}行` : '通用错误';
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(error);
  });
  
  return groups;
});

const handleClose = () => {
  emit('close');
};
</script>

<template>
  <a-modal
    :visible="visible"
    :title="title"
    :footer="null"
    width="600px"
    @cancel="handleClose"
  >
    <div class="validation-errors">
      <a-alert
        type="error"
        show-icon
        message="请修复以下错误后重新提交："
        class="mb-4"
      />
      
      <div class="error-groups">
        <div
          v-for="(errorList, groupName) in groupedErrors"
          :key="groupName"
          class="error-group mb-4"
        >
          <h4 class="error-group-title">{{ groupName }}</h4>
          <ul class="error-list">
            <li
              v-for="(error, index) in errorList"
              :key="index"
              class="error-item"
            >
              <a-tag color="red" class="error-field">{{ error.field }}</a-tag>
              <span class="error-message">{{ error.message }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    
    <template #footer>
      <a-button type="primary" @click="handleClose">
        我知道了
      </a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
.validation-errors {
  .error-groups {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .error-group {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;
    background-color: #fafafa;
    
    .error-group-title {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }
    
    .error-list {
      margin: 0;
      padding: 0;
      list-style: none;
      
      .error-item {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .error-field {
          margin-right: 8px;
          font-size: 12px;
        }
        
        .error-message {
          color: #595959;
          font-size: 13px;
        }
      }
    }
  }
}
</style>

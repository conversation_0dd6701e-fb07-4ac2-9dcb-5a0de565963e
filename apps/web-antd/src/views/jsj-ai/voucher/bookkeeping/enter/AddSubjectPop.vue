<script lang="ts" setup>
import { defineEmits, defineProps, onMounted, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { addSubjectSave, getSubjectCode, getSubjectInfo, saveSummary } from '#/api/account-book/bookkeeping/index';
import { addForeignSave } from '#/api/account-book/index';
import { useAssetType, useForeignCurrencyType } from '#/hooks/account-book/index';
import { useAbstractData, useSubjectData } from '#/hooks/account-book/voucher/index';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { VNodes } from '#/views/components/index';

import emitter from './usermitt';

defineOptions({
    name: 'AddSummaryPop',
});
const { operationType, operationItem } = defineProps<{
    operationItem?: any;
    operationType?: string;
}>();
const emits = defineEmits(['getSubjectData']);
const useLoading = useGlobalLoading();
const [Modal, ModalApi] = useVbenModal({
    onOpenChange: (type) => {
        if (type) {
            console.log('弹窗显示', operationType, operationItem);
            // 判断是编辑还是新增 如果是空字符串说明是新增凭证哪里使用的不用处理
            if (operationItem) {
                // 有父组件传进来的科目信息
                subjectChange(operationItem.id);
            }
        }
    },
});
const formRef = ref<any>(null);
const foreignFormRef = ref<any>(null);
const useSubject = useSubjectData();
const useForeignList = useForeignCurrencyType();
const useAssetList = useAssetType();
const categorylist = ref<any[]>([]); // 资产类型下的细分类别
const addForeign = ref<boolean>(false); // 添加外币名称
let superiorSubject: any;
const formState = reactive<any>({
    isStock: false, // 辅助核算 - 存货
    isPerson: false, // 辅助核算 - 个人
    isDepartment: false, // 辅助核算 - 部门
    isProject: false, // 辅助核算 - 项目
    isCustomer: false, // 辅助核算 - 客户
    isSupplier: false, // 辅助核算 - 供应商
    isQuantity: false, // 是否数量核算
    quantityUnit: '', // 计量单位
    isForCurrency: false, // 是否有外币核算
    isCountForCurrency: false, // 是否计算汇兑损益
    currency: null,
    parentId: undefined, // 上级科目id
    parentName: undefined, // 上级科目
    balanceDirection: undefined, // 余额方向
    isCashFlow: true, // 是否是现金流项目
    parentLeaf: false,
    code: '', // 科目编码
    name: '', // 科目名称
    currencyCode: undefined, // 对应币种
    currencyId: null, // 币种id
    typeId: undefined, // 资产类型
    categoryId: undefined, // 资产类型下的类别
    isBase: false, // 是否是基础科目
});
const foreignForm = reactive<any>({
    code: '', // 编码
    name: '', // 名称
});
ModalApi.onConfirm = async () => {
    /**
     * {
        "isForCurrency": false,
        "currency": null,
        "currencyId": null,
        "currencyCode": "KUN",
        "parentId": "9222721081396945475",
        "parentName": "1001 库存现金",
        "balanceDirection": "DEBIT",
        "isCashFlow": true,
        "parentLeaf": false,
        "isSupplier": true,
        "isCustomer": true,
        "isProject": true,
        "isDepartment": true,
        "isPerson": true,
        "isStock": true,
        "isQuantity": true,
        "code": "100101",
        "name": "测试数据1",
        "quantityUnit": "180",
        "typeId": "1",
        "categoryId": "1"
        }
     */
    formRef.value.validate().then((data: any) => {
        console.log('填写的数据', data);
        const todata: any = {
            isForCurrency: formState.isForCurrency, // 是否有外币核算
            currency: formState.currency,
            currencyId: formState.currencyId,
            parentId: formState.parentId,
            parentName: superiorSubject.text,
            balanceDirection: formState.balanceDirection,
            isCashFlow: formState.isCashFlow,
            parentLeaf: formState.parentLeaf,
            isSupplier: formState.isSupplier,
            isCustomer: formState.isCustomer,
            isProject: formState.isProject,
            isDepartment: formState.isDepartment,
            isPerson: formState.isPerson,
            isStock: formState.isStock,
            isQuantity: formState.isQuantity,
            code: formState.code,
            name: formState.name,
            quantityUnit: formState.quantityUnit,
            typeId: formState.typeId,
            categoryId: formState.categoryId,
        };
        if (todata.isForCurrency) {
            todata.isCountForCurrency = formState.isCountForCurrency;
            todata.currencyCode = formState.currencyCode;
        }
        useLoading.setShow(true);
        addSubjectSave(todata)
            .then((res: any) => {
                console.log(res);
                if (res.returnCode === '200') {
                    // 新增科目
                    useSubject.fetchData();
                    // 刷新
                    message.success('操作成功');
                    useSubject.fetchData(); // 重新获取数据
                    if (operationType === '') {
                        // 在设置凭证页面需要通知到凭证页面把当前新增的科目直接显示上去
                        emitter.emit('account_voucher_subject_added', res.data);
                    } else {
                        // 在科目期初哪里的处理
                        emits('getSubjectData');
                    }
                    ModalApi.close();
                } else {
                    message.warning(res.returnMsg);
                }
                useLoading.setShow(false);
            })
            .catch(() => {
                useLoading.setShow(false);
            });
    });
};
const subjectChangeAssignment = (data: any) => {
    superiorSubject = data;
    formState.parentLeaf = data.isLeaf && data.isAuxiliary;
    // 赋值资产类型
    formState.typeId = data.typeId;
    const itm = useAssetList.data.value.find((v: any) => v.id === formState.typeId);
    if (itm) {
        categorylist.value = itm.categorys;
    }
    // 获取资产类型的
    formState.categoryId = data.categoryId;
    // 余额方向
    formState.balanceDirection = data.balanceDirection;
    // 是否是现金流项目
    formState.isCashFlow = data.isCashFlow;
    // 如果是编辑的话要额外做赋值
    if (operationType === 'edit') {
        formState.name = data.name;
        formState.isSupplier = data.isSupplier;
        formState.isCustomer = data.isCustomer;
        formState.isProject = data.isProject;
        formState.isDepartment = data.isDepartment;
        formState.isPerson = data.isPerson;
        formState.isStock = data.isStock;

        formState.isQuantity = data.isQuantity;
        formState.quantityUnit = data.quantityUnit;

        formState.isForCurrency = data.isForCurrency;
        formState.currencyCode = data.currencyCode;
        formState.isCountForCurrency = data.isCountForCurrency;
        formState.isBase = data.isBase;
    }
};
const subjectChange = (value: string) => {
    getSubjectCode(value).then((res: any) => {
        console.log(res);
        if (res.returnCode === '200') {
            // 赋值科目编码
            formState.code = res.data;
        }
    });
    // 后去当前科目的详细信息这个数据本身是有的但是在不同的地方使用
    // 就统一到接口获取出来
    getSubjectInfo(value).then((res: any) => {
        if (res.returnCode === '200') {
            subjectChangeAssignment(res.data);
        }
    });
};
// 资产类型变化
const assetTypeChange = (value: string, data: any) => {
    console.log('资产类型变化', value, data);
    categorylist.value = data.categorys;
    formState.categoryId = data.categorys[0].id;
};
const addForeignClick = () => {
    addForeign.value = true;
};
const addForeignOk = () => {
    foreignFormRef.value.validate().then((data: any) => {
        addForeignSave(data).then((res: any) => {
            if (res.returnCode === '200') {
                formState.currencyCode = res.data.code;
                useForeignList.fetchData();
            } else {
                message.warning(res.returnMsg);
            }
        });
    });
};
// 是否禁用现金流选项
const getisCashFlow = () => {
    switch (operationType) {
        case 'add': {
            return true;
        }
        case 'edit': {
            // 当是可编辑的时候
            return !formState.isBase;
        }
        case undefined: {
            return true;
        }
        // No default
    }
};
// 借方贷方选择是否禁用
const getisBalanceDirection = () => {
    switch (operationType) {
        case 'add': {
            return false;
            break;
        }
        case 'edit': {
            // 当是可编辑的时候
            return formState.isBase;
        }
        case undefined: {
            return false;
        }
        // No default
    }
};
// 类型和类别是否禁用
const getisDisableAssetClass = () => {
    switch (operationType) {
        case 'add': {
            return false;
            break;
        }
        case 'edit': {
            // 当是可编辑的时候
            return formState.isBase;
        }
        case undefined: {
            return true;
        }
        // No default
    }
};
</script>
<template>
    <Modal :title="`${operationType === 'edit' ? '编辑' : '新增'}账簿科目`">
        <div>
            <a-form
                :model="formState"
                autocomplete="off"
                ref="formRef"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 18 }"
            >
                <a-form-item
                    label="上级科目"
                    name="parentId"
                    v-if="operationType === undefined"
                    :rules="[{ required: true, message: '请选择上级科目' }]"
                >
                    <a-select
                        v-model:value="formState.parentId"
                        placeholder="请选择"
                        show-search
                        :field-names="{
                            label: 'text',
                            value: 'id',
                        }"
                        @change="subjectChange"
                        :options="useSubject.selectdata.value"
                    />
                </a-form-item>
                <a-form-item
                    label="编码"
                    name="code"
                    :rules="[{ required: true, message: '请选择' }]"
                >
                    <a-input
                        disabled
                        v-model:value="formState.code"
                    />
                </a-form-item>
                <a-form-item
                    label="名称"
                    name="name"
                    :rules="[{ required: true, message: '请输入名称' }]"
                >
                    <a-input v-model:value="formState.name" />
                </a-form-item>
                <a-form-item
                    label="类型"
                    name="typeId"
                    :rules="[{ required: true, message: '请选择资产类型' }]"
                >
                    <a-select
                        v-model:value="formState.typeId"
                        :options="useAssetList.data.value"
                        :disabled="getisDisableAssetClass()"
                        @change="assetTypeChange"
                        :field-names="{
                            label: 'name',
                            value: 'id',
                        }"
                    />
                </a-form-item>
                <a-form-item
                    label="类别"
                    name="categoryId"
                    :rules="[{ required: true, message: '请选择资产类型中的细分类别' }]"
                >
                    <a-select
                        v-model:value="formState.categoryId"
                        :disabled="getisDisableAssetClass()"
                        :field-names="{
                            label: 'name',
                            value: 'id',
                        }"
                        :options="categorylist"
                    />
                </a-form-item>
                <a-form-item
                    label="余额方向"
                    name="balanceDirection"
                    :rules="[{ required: true, message: '请选择余额方向' }]"
                >
                    <a-radio-group
                        v-model:value="formState.balanceDirection"
                        :disabled="getisBalanceDirection()"
                    >
                        <a-radio value="DEBIT">借方</a-radio>
                        <a-radio value="CREDIT">贷方</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item
                    label="现金流量"
                    name="type"
                >
                    <a-checkbox
                        name="type"
                        :disabled="getisCashFlow()"
                        v-model:checked="formState.isCashFlow"
                    >
                        是否现金流量项目&nbsp;<span
                            v-if="formState.isCashFlow && !formState.isBase && operationType === 'edit'"
                            class="reminder"
                        >
                            提示：现金流量项目只能从上级科目继承，如有需要请修改一级科目的现金流量项目设置
                        </span>
                    </a-checkbox>
                </a-form-item>
                <a-form-item
                    label="数量核算"
                    name="type"
                >
                    <a-checkbox
                        value="1"
                        name="type"
                        v-model:checked="formState.isQuantity"
                    >
                        是否数量核算
                    </a-checkbox>
                </a-form-item>
                <a-form-item
                    label="计量单位"
                    v-if="formState.isQuantity"
                >
                    <a-input
                        v-model:value="formState.quantityUnit"
                        maxlength="10"
                    />
                </a-form-item>
                <a-form-item
                    label="辅助核算"
                    name="type"
                >
                    <a-checkbox v-model:checked="formState.isSupplier"> 供应商 </a-checkbox>
                    <a-checkbox v-model:checked="formState.isCustomer"> 客户 </a-checkbox>
                    <a-checkbox v-model:checked="formState.isProject"> 项目 </a-checkbox>
                    <a-checkbox v-model:checked="formState.isDepartment"> 部门 </a-checkbox>
                    <a-checkbox v-model:checked="formState.isPerson"> 个人 </a-checkbox>
                    <a-checkbox v-model:checked="formState.isStock"> 存货 </a-checkbox>
                </a-form-item>
                <a-form-item
                    label="外币核算"
                    name="type"
                >
                    <a-checkbox v-model:checked="formState.isForCurrency"> 是否外币核算 </a-checkbox>
                    <a-checkbox
                        v-if="formState.isForCurrency"
                        v-model:checked="formState.isCountForCurrency"
                    >
                        是否计算汇兑损益
                    </a-checkbox>
                </a-form-item>
                <a-form-item
                    label="对应币种"
                    name="currencyCode"
                    placeholder="请选择"
                    v-if="formState.isForCurrency"
                    :rules="[{ required: true, message: '请选择币种' }]"
                >
                    <a-select
                        v-model:value="formState.currencyCode"
                        :options="useForeignList.data.value.map((item: any) => ({ label: `${item.code}-${item.name}`, value: item.code }))"
                    >
                        <template #dropdownRender="{ menuNode: menu }">
                            <VNodes :vnodes="menu" />
                            <a-divider style="margin: 4px 0" />
                            <div class="btn">
                                <a-button
                                    @click="addForeignClick"
                                    size="small"
                                >
                                    <PlusOutlined />增加新的币种
                                </a-button>
                            </div>
                        </template>
                    </a-select>
                </a-form-item>
            </a-form>
        </div>
        <a-modal
            v-model:open="addForeign"
            @ok="addForeignOk"
            title="外币管理"
        >
            <a-form
                ref="foreignFormRef"
                :model="foreignForm"
            >
                <a-form-item
                    label="编码"
                    name="code"
                    placeholder="请输入英文字母大写编码"
                    :rules="[{ required: true, message: '请输入英文字母大写编码' }]"
                >
                    <a-input v-model:value="foreignForm.code" />
                </a-form-item>
                <a-form-item
                    label="名称"
                    name="name"
                    placeholder="请输入名称"
                    :rules="[{ required: true, message: '请输入名称' }]"
                >
                    <a-input v-model:value="foreignForm.name" />
                </a-form-item>
            </a-form>
        </a-modal>
    </Modal>
</template>
<style lang="scss" scoped>
.btn {
    text-align: center;
}

.reminder {
    font-size: 10px;
    color: red;
}
</style>

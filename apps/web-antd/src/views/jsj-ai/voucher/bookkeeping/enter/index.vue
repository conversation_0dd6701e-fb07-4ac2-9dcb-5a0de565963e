<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { useUserStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { setVoucherSave } from '#/api/account-book/bookkeeping/index';
import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';
import { useMonthSelectionStore } from '#/store/modules/month-selection';

import VoucherEditing from './VoucherEditing.vue';
import ValidationErrorModal from '../components/ValidationErrorModal.vue';
import { showValidationErrors } from '../utils/validation';

import type { ValidationError } from '../utils/validation';

const router = useRouter();
const userStore = useUserStore();
const monthSelectionStore = useMonthSelectionStore();

// 公司选择
const { 
  selectedCompany, 
  companyOptions, 
  fetchCompanyNames 
} = useCompanySelection();

// VoucherEditing组件引用
const voucherEditingRef = ref<any>(null);

// 加载状态
const loading = ref(false);
const saving = ref(false);

// 校验错误模态框
const validationErrorVisible = ref(false);
const validationErrors = ref<ValidationError[]>([]);

// 初始化
onMounted(async () => {
  try {
    await fetchCompanyNames();
  } catch (error) {
    console.error('获取公司列表失败:', error);
    message.error('获取公司列表失败');
  }
});

// 保存凭证
async function handleSave() {
  if (!voucherEditingRef.value) {
    message.error('凭证编辑组件未加载');
    return;
  }

  try {
    saving.value = true;

    // 获取凭证数据
    const voucherData = voucherEditingRef.value.state;
    if (!voucherData || !voucherData.list) {
      message.error('无法获取凭证数据');
      return;
    }

    // 校验数据
    const validationResult = voucherEditingRef.value.data_validation();
    if (!validationResult.success) {
      if (validationResult.errors && validationResult.errors.length > 0) {
        // 显示详细错误信息
        validationErrors.value = validationResult.errors;
        validationErrorVisible.value = true;
      } else {
        // 显示简单错误信息
        message.error(validationResult.message);
      }
      return;
    }

    // 检查必要信息
    if (!selectedCompany.value) {
      message.error('请先选择公司');
      return;
    }

    const month = monthSelectionStore.getFormattedMonth();
    if (!month) {
      message.error('请先选择月份');
      return;
    }

    const username = userStore.userInfo?.username;
    if (!username) {
      message.error('无法获取用户信息，请重新登录');
      return;
    }

    // 构造保存数据
    const saveData = {
      company_name: selectedCompany.value,
      month,
      username,
      voucher_date: new Date().toISOString().split('T')[0],
      voucher_type: '记账凭证',
      details: voucherData.list
        .filter((item: any) => item.abstract || item.subject || item.borrower || item.lender)
        .map((item: any, index: number) => ({
          id: index + 1,
          summary: typeof item.abstract === 'string' ? item.abstract : item.abstract?.text || '',
          account: item.subject?.text || item.subject?.name || '',
          debit: Number(item.borrower) || 0,
          credit: Number(item.lender) || 0
        }))
    };

    // 调用保存API
    const result = await setVoucherSave(saveData);

    if (result.returnCode !== '200') {
      throw new Error(result.returnMsg || '保存失败');
    }

    message.success('凭证保存成功');

    // 清空表格数据
    if (voucherEditingRef.value.clearTableData) {
      voucherEditingRef.value.clearTableData();
    }

    // 可选：跳转到查看页面
    // router.push('/bookkeeping/view');

  } catch (error) {
    console.error('保存凭证失败:', error);
    message.error('保存凭证失败，请重试');
  } finally {
    saving.value = false;
  }
}

// 保存为模板
async function handleSaveAsTemplate() {
  if (!voucherEditingRef.value) {
    message.error('凭证编辑组件未加载');
    return;
  }

  try {
    saving.value = true;

    // 校验数据（模板模式）
    const validationResult = voucherEditingRef.value.data_validation('template');
    if (!validationResult.success) {
      if (validationResult.errors && validationResult.errors.length > 0) {
        validationErrors.value = validationResult.errors;
        validationErrorVisible.value = true;
      } else {
        message.error(validationResult.message);
      }
      return;
    }

    // TODO: 实现保存模板的逻辑
    message.success('模板保存成功');

  } catch (error) {
    console.error('保存模板失败:', error);
    message.error('保存模板失败，请重试');
  } finally {
    saving.value = false;
  }
}

// 清空数据
function handleClear() {
  if (voucherEditingRef.value && voucherEditingRef.value.clearTableData) {
    voucherEditingRef.value.clearTableData();
    message.success('数据已清空');
  }
}

// 关闭校验错误模态框
function handleCloseValidationError() {
  validationErrorVisible.value = false;
  validationErrors.value = [];
}
</script>

<template>
  <div class="voucher-enter-page">
    <!-- 页面头部 -->
    <a-card class="header-card" :bordered="false">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">凭证录入</h2>
          <span class="page-desc">录入和编辑记账凭证</span>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleClear">
              清空
            </a-button>
            <a-button 
              @click="handleSaveAsTemplate"
              :loading="saving"
            >
              保存为模板
            </a-button>
            <a-button 
              type="primary" 
              @click="handleSave"
              :loading="saving"
            >
              保存凭证
            </a-button>
          </a-space>
        </div>
      </div>
    </a-card>

    <!-- 凭证录入区域 -->
    <a-card title="凭证信息" class="voucher-card" :bordered="false">
      <VoucherEditing ref="voucherEditingRef" />
    </a-card>

    <!-- 校验错误模态框 -->
    <ValidationErrorModal
      :visible="validationErrorVisible"
      :errors="validationErrors"
      title="数据校验失败"
      @close="handleCloseValidationError"
    />
  </div>
</template>

<style scoped lang="scss">
.voucher-enter-page {
  min-height: 100vh;
  padding: 16px;
  background-color: #f5f5f5;

  .header-card {
    margin-bottom: 16px;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-left {
        .page-title {
          margin: 0 0 4px;
          font-size: 20px;
          font-weight: 600;
          color: #262626;
        }

        .page-desc {
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }
  }

  .voucher-card {
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}
</style>

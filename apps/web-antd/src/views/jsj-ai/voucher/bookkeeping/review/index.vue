<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import { message } from 'ant-design-vue';

  import {
    getCurrentVouchers,
    updateVoucher,
    writeBackVouchers,
  } from '#/api/original-voucher/api-v2';
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';
  import { useCurrentCustomerStore } from '#/store/account-book/company';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';
  import { useVoucherStore } from '#/store/modules/voucher';

  import VoucherEditing from '../enter/VoucherEditing.vue';
  import OriginalFilesDisplay from './components/OriginalFilesDisplay.vue';

  const route = useRoute();
  const monthSelectionStore = useMonthSelectionStore();
  const voucherStore = useVoucherStore();
  const customerStore = useCurrentCustomerStore();
  const userStore = useUserStore();
  const { fetchCompanyNames, selectedCompany } = useCompanySelection();

  // 响应式数据
  const loading = ref(false);
  const voucherData = ref<any>(null);
  const originalFilesData = ref<any>(null);
  const voucherId = ref<string>('');
  const voucherEditingRef = ref<any>(null);
  const reviewVoucherInfo = ref<any>(null);
  const voucherEditingKey = ref<number>(0);

  // 测试会计科目选择器
  const testSubjectValue = ref<string>('');

  // 处理科目选择变化
  const handleSubjectChange = (value: string, option: any) => {
    console.log('科目选择变化:', { option, value });
    message.info(`选择了科目: ${option.label}`);
  };

  // 初始化页面数据
  onMounted(async () => {
    // 获取公司列表
    try {
      await fetchCompanyNames();

      // 优先从store获取凭证数据
      if (voucherStore.reviewVoucherData) {
        reviewVoucherInfo.value = voucherStore.reviewVoucherData;
        voucherId.value = voucherStore.reviewVoucherData.id;

        // 设置凭证数据到VoucherEditing组件
        setVoucherDataToEditing(voucherStore.reviewVoucherData);

        // 尝试获取原始文件数据
        await loadOriginalFilesData();
      } else {
        // 如果store中没有数据，尝试从URL参数获取凭证ID
        voucherId.value = (route.query.voucherId as string) || '';

        if (!voucherId.value) {
          message.warning('未指定要审核的凭证');
          return;
        }

        // 通过API加载凭证数据
        await loadVoucherData();
      }
    } catch (error) {
      console.error('初始化失败:', error);
      message.error('页面初始化失败');
    }
  });

  // 加载凭证数据
  async function loadVoucherData() {
    if (!voucherId.value) return;

    loading.value = true;
    try {
      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.warning('请先选择公司和月份');
        return;
      }

      // 从getCurrentVouchers接口获取凭证数据
      const response = await getCurrentVouchers({
        company_name: companyName,
        month,
      });

      const res = (response as any).data;
      if (res && res.items) {
        // 查找匹配的凭证
        const targetVoucher = res.items.find(
          (item: any) =>
            item.voucher.unique_id === voucherId.value ||
            item.voucher.id.toString() === voucherId.value,
        );

        if (targetVoucher) {
          voucherData.value = targetVoucher.voucher;
          originalFilesData.value = targetVoucher.source_info;
        } else {
          message.error('未找到指定的凭证');
        }
      } else {
        message.error('获取凭证数据失败');
      }
    } catch (error) {
      console.error('获取凭证数据失败:', error);
      message.error('获取凭证数据失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 保存审核结果
  async function handleSaveReview() {
    if (!voucherEditingRef.value) {
      message.error('凭证编辑组件未加载');
      return;
    }

    try {
      loading.value = true;

      // 获取VoucherEditing组件中的数据
      const voucherEditingData = voucherEditingRef.value.state;

      if (!voucherEditingData || !voucherEditingData.list) {
        message.error('无法获取凭证数据');
        return;
      }

      // 验证凭证数据
      const validationResult = voucherEditingRef.value.data_validation();
      if (!validationResult.success) {
        message.error(`凭证数据验证失败：${validationResult.message}`);
        return;
      }

      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.error('请先选择公司和月份');
        return;
      }

      if (!reviewVoucherInfo.value || !reviewVoucherInfo.value.id) {
        message.error('缺少凭证ID信息');
        return;
      }

      // 转换VoucherEditing组件的数据格式为API所需格式
      const details = voucherEditingData.list
        .filter((item: any) => item.subject && (item.borrower || item.lender))
        .map((item: any, index: number) => ({
          account: item.subject.text || '',
          credit: Number.parseFloat(item.lender) || 0,
          debit: Number.parseFloat(item.borrower) || 0,
          id: index + 1,
          summary: item.abstract?.text || '',
        }));

      if (details.length === 0) {
        message.error('请至少添加一条有效的凭证明细');
        return;
      }

      // 构建更新凭证的参数
      const updateData = {
        company_name: companyName,
        month,
        voucher: {
          details,
          id: Number.parseInt(reviewVoucherInfo.value.id) || 1,
          record_date:
            reviewVoucherInfo.value.date ||
            new Date().toISOString().split('T')[0],
          type: reviewVoucherInfo.value.type || '记',
        },
        voucher_unique_id: reviewVoucherInfo.value.id,
      };

      console.log('保存凭证数据:', updateData);

      // 调用updateVoucher API
      await updateVoucher(updateData);

      message.success('凭证保存成功');

      // 更新本地数据
      if (reviewVoucherInfo.value) {
        reviewVoucherInfo.value.detail = details.map((detail) => ({
          credit: detail.credit,
          debit: detail.debit,
          id: detail.id,
          subjectName: detail.account,
          summary: detail.summary,
        }));
      }
    } catch (error) {
      console.error('保存凭证失败:', error);
      message.error('保存凭证失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 写入凭证
  async function handleWriteBackVoucher() {
    if (!reviewVoucherInfo.value || !reviewVoucherInfo.value.id) {
      message.warning('未找到要写入的凭证');
      return;
    }

    try {
      loading.value = true;

      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.warning('请先选择公司和月份');
        return;
      }

      // 获取当前用户信息
      const username = userStore.userInfo?.username || '';

      if (!username) {
        message.error('无法获取用户信息，请重新登录');
        return;
      }

      // 调用凭证写入API
      const result = await writeBackVouchers({
        company_name: companyName,
        month,
        username,
        voucher_ids: [reviewVoucherInfo.value.id],
      });

      if (result.success) {
        message.success('凭证写入成功');

        // 可以选择跳转回查看凭证页面
        // router.push('/bookkeeping/view');
      } else {
        message.error(`凭证写入失败：${result.message}`);
      }
    } catch (error: any) {
      console.error('写入凭证失败:', error);
      message.error(`写入凭证失败：${error?.message || '未知错误'}`);
    } finally {
      loading.value = false;
    }
  }

  // 设置凭证数据到编辑组件
  function setVoucherDataToEditing(reviewData: any) {
    // 转换数据格式以适配VoucherEditing组件的ListItm格式
    const voucherEditingData = reviewData.detail.map(
      (item: any, index: number) => {
        const borrowerValue = item.debit > 0 ? item.debit.toString() : '';
        const lenderValue = item.credit > 0 ? item.credit.toString() : '';

        return {
          abstract: { text: item.summary }, // 摘要对象格式
          balance: 0, // 余额
          borrower: borrowerValue, // 借方金额
          lender: lenderValue, // 贷方金额
          subject: {
            balance: 0, // 默认余额
            code: '', // 科目代码
            text: item.subjectName,
          }, // 科目对象格式
        };
      },
    );

    // 确保至少有4行数据（VoucherEditing组件的默认要求）
    while (voucherEditingData.length < 4) {
      voucherEditingData.push({
        abstract: undefined,
        balance: 0,
        borrower: '',
        lender: '',
        subject: undefined,
      });
    }

    // 将数据存储到localStorage，这样VoucherEditing组件就能读取到
    const storageKey = `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`;
    const customerIdKey = `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`;

    localStorage.setItem(storageKey, JSON.stringify(voucherEditingData));

    // 重要：同时设置公司ID，确保VoucherEditing组件能正确读取数据
    // VoucherEditing组件使用useCurrentCustomerStore().customerId进行验证
    // 但是useCurrentCustomerStore没有customerId属性，我们需要创建一个兼容的值
    const currentCompanyName = selectedCompany.value;

    // 为了兼容VoucherEditing组件的验证逻辑，我们需要确保customerId有值
    // 如果customerStore.currentCompany存在，使用其id，否则使用公司名称
    const customerId =
      customerStore.currentCompany?.id || currentCompanyName || 'default';

    // 同时，我们需要确保customerStore有customerId属性供VoucherEditing组件使用
    // 这是一个临时解决方案，直到VoucherEditing组件被修复
    if (!customerStore.customerId) {
      (customerStore as any).customerId = customerId;
    }

    localStorage.setItem(customerIdKey, customerId);

    console.log('存储凭证数据到localStorage:', {
      currentCompanyName,
      customerId,
      customerIdKey,
      'customerStore.currentCompany': customerStore.currentCompany,
      storageKey,
      voucherEditingData,
    });

    // 同时设置voucherData用于显示
    voucherData.value = {
      code: reviewData.code,
      date: reviewData.date,
      details: reviewData.detail,
      id: reviewData.id,
      total_credit: reviewData.credit,
      total_debit: reviewData.debit,
      type: reviewData.type,
    };

    // 更新key来强制重新渲染VoucherEditing组件
    voucherEditingKey.value += 1;
  }

  // 加载原始文件数据
  async function loadOriginalFilesData() {
    if (!voucherId.value) {
      return;
    }

    loading.value = true;
    try {
      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      console.log('加载原始文件数据 - 参数检查:', {
        companyName,
        month,
        voucherId: voucherId.value,
      });

      if (!companyName || !month) {
        console.warn('公司名称或月份未设置，跳过原始文件加载');
        return;
      }

      console.log('开始获取原始文件数据:', {
        companyName,
        month,
        voucherId: voucherId.value,
      });

      // 从getCurrentVouchers接口获取凭证数据
      const response = await getCurrentVouchers({
        company_name: companyName,
        month,
      });
      console.log('原始文件数据响应:', response);

      const res = (response as any).data;

      if (res && res.items) {
        // 查找匹配的凭证
        const targetVoucher = res.items.find((item: any) => {
          return (
            item.voucher.unique_id === voucherId.value ||
            item.voucher.id.toString() === voucherId.value
          );
        });

        if (targetVoucher && targetVoucher.source_info) {
          originalFilesData.value = targetVoucher.source_info;
        }
      }
    } catch (error) {
      console.error('获取原始文件数据失败:', error);
      // 不显示错误消息，因为原始文件数据不是必需的
    } finally {
      loading.value = false;
    }
  }

  // 监听store中审核凭证数据的变化
  watch(
    () => voucherStore.reviewVoucherData,
    async (newData) => {
      if (newData) {
        reviewVoucherInfo.value = newData;
        voucherId.value = newData.id;
        setVoucherDataToEditing(newData);

        // 同时加载原始文件数据
        await loadOriginalFilesData();
      }
    },
    { immediate: true },
  );
</script>

<template>
  <div class="voucher-review-page">
    <!-- 页面标题栏 -->
    <a-card class="review-header" :bordered="false">
      <div class="header-content">
        <div class="header-title">
          <h2>凭证审核</h2>
          <div v-if="reviewVoucherInfo" class="voucher-info">
            <a-tag color="blue">{{ reviewVoucherInfo.code }}</a-tag>
            <a-tag color="green">{{ reviewVoucherInfo.date }}</a-tag>
            <a-tag :color="reviewVoucherInfo.confirmed ? 'success' : 'warning'">
              {{ reviewVoucherInfo.confirmed ? '已确认' : '待确认' }}
            </a-tag>
          </div>
          <a-tag v-else-if="voucherId" color="default">
            凭证ID: {{ voucherId }}
          </a-tag>
        </div>
        <a-space>
          <a-button @click="loadVoucherData" :loading="loading">
            刷新数据
          </a-button>
          <a-button type="primary" @click="handleSaveReview">保存</a-button>
          <a-button
            type="primary"
            @click="handleWriteBackVoucher"
            :loading="loading"
          >
            写入凭证
          </a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 主要内容区域 -->
    <div class="review-content">
      <!-- 凭证录入区域 -->
      <a-card title="凭证录入" class="voucher-editing-card" :bordered="false">
        <template #extra>
          <span class="card-desc">编辑和完善凭证信息</span>
        </template>
        <VoucherEditing ref="voucherEditingRef" :key="voucherEditingKey" />
      </a-card>

      <!-- 原始文件展示区域 -->
      <a-card title="原始文件" class="original-files-card" :bordered="false">
        <template #extra>
          <span class="card-desc">查看相关的原始凭证文件</span>
        </template>
        <OriginalFilesDisplay
          v-if="originalFilesData"
          :files-data="originalFilesData"
        />
        <a-empty v-else description="暂无原始文件" />
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .voucher-review-page {
      gap: 6px;
      padding: 6px;
    }

    .review-header {
      :deep(.ant-card-body) {
        padding: 8px 12px;
      }

      .header-content {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;

        .header-title {
          flex-direction: column;
          gap: 6px;
          align-items: flex-start;

          h2 {
            font-size: 14px;
          }
        }
      }
    }

    .voucher-editing-card,
    .original-files-card {
      :deep(.ant-card-head) {
        min-height: 36px;
        padding: 0 12px;

        .ant-card-head-title {
          padding: 6px 0;
          font-size: 13px;
        }

        .ant-card-extra {
          padding: 6px 0;
        }
      }

      :deep(.ant-card-body) {
        padding: 8px;
      }

      .card-desc {
        font-size: 10px;
      }
    }
  }

  // 小屏幕优化
  @media (max-width: 480px) {
    .voucher-review-page {
      gap: 4px;
      padding: 4px;
    }

    .review-header {
      :deep(.ant-card-body) {
        padding: 6px 8px;
      }

      .header-content {
        .header-title h2 {
          font-size: 13px;
        }
      }
    }

    .voucher-editing-card,
    .original-files-card {
      :deep(.ant-card-head) {
        min-height: 32px;
        padding: 0 8px;

        .ant-card-head-title {
          padding: 4px 0;
          font-size: 12px;
        }
      }

      :deep(.ant-card-body) {
        padding: 6px;
      }
    }
  }

  .voucher-review-page {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 100vh;
    padding: 8px;
    background: #f5f5f5;
  }

  .review-header {
    :deep(.ant-card-body) {
      padding: 12px 16px;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-title {
        display: flex;
        gap: 8px;
        align-items: center;

        h2 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .voucher-info {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          align-items: center;
        }
      }
    }
  }

  .review-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .voucher-editing-card {
    :deep(.ant-card-head) {
      min-height: 40px;
      padding: 0 16px;

      .ant-card-head-title {
        padding: 8px 0;
        font-size: 14px;
        font-weight: 500;
      }

      .ant-card-extra {
        padding: 8px 0;
      }
    }

    :deep(.ant-card-body) {
      padding: 12px;
    }

    .card-desc {
      font-size: 11px;
      color: #8c8c8c;
    }
  }

  .original-files-card {
    :deep(.ant-card-head) {
      min-height: 40px;
      padding: 0 16px;

      .ant-card-head-title {
        padding: 8px 0;
        font-size: 14px;
        font-weight: 500;
      }

      .ant-card-extra {
        padding: 8px 0;
      }
    }

    :deep(.ant-card-body) {
      padding: 12px;
    }

    .card-desc {
      font-size: 11px;
      color: #8c8c8c;
    }
  }
</style>

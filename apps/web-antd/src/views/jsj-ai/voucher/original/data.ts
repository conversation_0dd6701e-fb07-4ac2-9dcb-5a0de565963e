// 发票和银行回单tab的表格字段和筛选schema定义
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { FormSchemaGetter } from '#/adapter/form';

// 公司选项接口
export interface CompanyOption {
  label: string;
  value: string;
}

// 发票列表表格字段
export const invoiceColumns: VxeGridProps['columns'] = [
  {
    title: '发票号码',
    field: 'digital_invoice_number',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '凭证号',
    field: 'voucher_number',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '发票状态',
    field: 'status',
    width: 100,
  },
  {
    title: '发票类型',
    field: 'type',
    width: 120,
  },
  {
    title: '开票日期',
    field: 'issue_date',
    width: 120,
  },
  {
    title: '货物或劳务明细',
    field: 'goods_name',
    minWidth: 200,
    showOverflow: 'tooltip',
  },
  {
    title: '销方名称',
    field: 'seller_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '购方名称',
    field: 'buyer_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '税率',
    field: 'tax_rate',
    width: 80,
    align: 'center',
  },
  {
    title: '备注',
    field: 'remark',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 发票列表筛选schema
export const invoiceQuerySchema = (companyOptions: CompanyOption[] = [], defaultCompany = '', defaultInputOutput = 'output') => [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '开票日期',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '发票状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '正常', value: '0' },
        { label: '已作废', value: '2' },
      ],
      allowClear: true,
      placeholder: '请选择状态',
    },
  },
  {
    component: 'Select',
    fieldName: 'company_name',
    label: '公司名称',
    defaultValue: defaultCompany,
    componentProps: {
      options: companyOptions,
      placeholder: '请选择公司名称',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
];

// 银行回单表格字段
export const bankColumns: VxeGridProps['columns'] = [
  {
    title: '公司名称',
    field: 'company_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '交易时间',
    field: 'transaction_time',
    width: 150,
  },
  {
    title: '月份',
    field: 'month',
    width: 80,
  },
  {
    title: '类型',
    field: 'type',
    width: 80,
  },
  {
    title: '凭证编号',
    field: 'voucher_num',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '交易流水号',
    field: 'transaction_id',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '账户名称',
    field: 'account_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '银行名称',
    field: 'bank_name',
    width: 120,
  },
  {
    title: '对方账户名称',
    field: 'conterpary_account_name',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '金额',
    field: 'amount',
    width: 120,
    align: 'right',
  },
  {
    title: '币种',
    field: 'currency',
    width: 60,
  },
  {
    title: '摘要',
    field: 'summary',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '备注',
    field: 'note',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 工资单表格字段
export const payrollColumns: VxeGridProps['columns'] = [
  {
    title: '姓名',
    field: 'name',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '员工编号',
    field: 'employee_id',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '身份证号',
    field: 'id_number',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '基本工资',
    field: 'basic_salary',
    width: 100,
    align: 'right',
  },
  {
    title: '津贴',
    field: 'allowances',
    width: 80,
    align: 'right',
  },
  {
    title: '社保扣除',
    field: 'social_insurance_deduction',
    width: 100,
    align: 'right',
  },
  {
    title: '公积金扣除',
    field: 'housing_fund_deduction',
    width: 100,
    align: 'right',
  },
  {
    title: '个人所得税',
    field: 'income_tax',
    width: 100,
    align: 'right',
  },
  {
    title: '实发工资',
    field: 'net_salary',
    width: 100,
    align: 'right',
  },
  {
    title: '工作天数',
    field: 'work_days',
    width: 80,
    align: 'center',
  },
  {
    title: '加班小时',
    field: 'overtime_hours',
    width: 80,
    align: 'center',
  },
  {
    title: '月份',
    field: 'month',
    width: 80,
  },
  {
    title: '备注',
    field: 'remark',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 工资单筛选schema
export const payrollQuerySchema = (companyOptions: CompanyOption[] = [], defaultCompany = '') => [
  {
    component: 'Select',
    fieldName: 'company_name',
    label: '公司名称',
    required: true,
    defaultValue: defaultCompany,
    componentProps: {
      options: companyOptions,
      placeholder: '请选择公司名称',
      allowClear: true,
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
  {
    component: 'MonthPicker',
    fieldName: 'month',
    label: '月份',
    required: true,
    defaultValue: (() => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      return `${year}${month}`;
    })(), // 当前月份，格式如202502
    componentProps: {
      placeholder: '请选择月份',
      format: 'YYYY年MM月',
      valueFormat: 'YYYYMM',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '姓名',
    componentProps: {
      placeholder: '请输入姓名',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'id_number',
    label: '身份证号',
    componentProps: {
      placeholder: '请输入身份证号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'voucher_num',
    label: '凭证编号',
    componentProps: {
      placeholder: '请输入凭证编号',
      allowClear: true,
    },
  },
];

// 银行回单筛选schema
export const bankQuerySchema = (companyOptions: CompanyOption[] = [], defaultCompany = '') => [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '交易时间',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'voucher_num',
    label: '凭证编号',
    componentProps: { placeholder: '请输入凭证编号' },
  },
  {
    component: 'MonthPicker',
    fieldName: 'month',
    label: '月份',
    // defaultValue: (() => {
    //   const now = new Date();
    //   const year = now.getFullYear();
    //   const month = String(now.getMonth() + 1).padStart(2, '0');
    //   return `${year}${month}`;
    // })(), // 当前月份，格式如202502
    componentProps: {
      placeholder: '请选择月份',
      format: 'YYYY年MM月',
      valueFormat: 'YYYYMM',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '类型',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '收入', value: '收入' },
        { label: '支出', value: '支出' },
      ],
      allowClear: true,
      placeholder: '请选择类型',
    },
  },
  {
    component: 'Select',
    fieldName: 'company_name',
    label: '公司名称',
    required: true,
    defaultValue: defaultCompany,
    componentProps: {
      options: companyOptions,
      placeholder: '请选择公司名称',
      allowClear: true,
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
]; 

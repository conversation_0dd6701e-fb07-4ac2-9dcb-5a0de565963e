<script setup lang="ts">
  import type { FormInstance } from 'ant-design-vue';

  import { computed, ref, watch } from 'vue';

  import { useSceneOptions } from '#/hooks/useSceneOptions';

  interface UpdateBankReceiptSceneFormData {
    br_account_name: string;
    br_conterpary_account_name: string;
    br_currency: string;
    br_note: string;
    br_summary: string;
    // 其他字段保持默认值，不在表单中显示
    br_type: string;
    scene: string;
  }

  interface Props {
    companyName: string;
    loading?: boolean;
    selectedCount: number;
    visible: boolean;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'confirm', data: UpdateBankReceiptSceneFormData): void;
    (e: 'cancel'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  const emit = defineEmits<Emits>();

  const formRef = ref<FormInstance>();

  // 默认值配置
  const defaultValues = {
    br_account_name: '',
    br_conterpary_account_name: '',
    br_currency: 'CNY',
    br_note: '',
    br_summary: '',
    br_type: '',
  };

  const formData = ref<UpdateBankReceiptSceneFormData>({
    scene: '',
    ...defaultValues,
  });

  // 使用场景选择hook
  const companyNameRef = {
    value: computed(() => props.companyName),
  };
  const { loading: loadingScenes, sceneOptions, fetchSceneOptions } =
    useSceneOptions(companyNameRef);

  // 监听visible变化，重置表单并获取场景数据
  watch(
    () => props.visible,
    (newVisible) => {
      if (newVisible) {
        resetForm();
        // 当模态框打开时，主动获取场景数据
        if (props.companyName) {
          fetchSceneOptions();
        }
      }
    },
  );

  function resetForm() {
    formData.value = {
      scene: '',
      ...defaultValues,
    };
    formRef.value?.clearValidate();
  }

  async function handleConfirm() {
    try {
      await formRef.value?.validate();
      emit('confirm', { ...formData.value });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  }

  function handleCancel() {
    formData.value.scene = '';
    emit('cancel');
    emit('update:visible', false);
  }
</script>

<template>
  <a-modal
    :open="visible"
    title="批量更新银行回单场景"
    width="500px"
    :confirm-loading="loading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="update-scene-form">
      <a-form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="场景名称"
          name="scene"
          :rules="[{ required: true, message: '请选择场景名称' }]"
        >
          <a-select
            v-model:value="formData.scene"
            :loading="loadingScenes"
            :options="sceneOptions"
            placeholder="请选择场景名称"
            show-search
            :filter-option="
              (input: string, option: any) =>
                option?.label?.toLowerCase().includes(input.toLowerCase())
            "
          />
        </a-form-item>
      </a-form>

      <div class="selected-info">
        <a-alert
          :message="`已选择 ${selectedCount} 条银行回单记录`"
          type="info"
          show-icon
        />
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
  .update-scene-form {
    padding: 16px 0;
  }

  .selected-info {
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
</style>

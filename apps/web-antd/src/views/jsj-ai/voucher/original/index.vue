<script setup lang="ts">
  import type { CompanyOption } from './data';

  import type {
    BankReceiptData,
    BankReceiptQueryParams,
    BankReceiptUpdateSceneParams,
    InvoiceData,
    InvoiceQueryParams,
    InvoiceUpdateSceneParams,
    PayrollData,
    PayrollQueryParams,
  } from '#/api/original-voucher/types';

  import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';

  import { Button, message } from 'ant-design-vue';

  import {
    fetchBankReceiptList,
    fetchInvoiceList,
    fetchPayrollList,
    updateBankReceiptScene,
    updateInvoiceScene,
  } from '#/api/original-voucher/api-v2';
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';

  import FilterForm from './components/FilterForm.vue';
  import UpdateBankReceiptSceneModal from './components/UpdateBankReceiptSceneModal.vue';
  import UpdateSceneModal from './components/UpdateSceneModal.vue';
  import {
    bankQuerySchema,
    invoiceQuerySchema,
    payrollQuerySchema,
  } from './data';

  const tabList = [
    { key: 'output', label: '销项发票' },
    { key: 'input', label: '进项发票' },
    { key: 'bank', label: '银行回单' },
    { key: 'payroll', label: '工资单' },
  ];
  const activeTab = ref('output');

  // 动态schema和columns
  const formSchema = computed(() => {
    // 使用表单中当前选中的公司作为默认值
    const currentCompany = formData.value.company_name || '';

    if (activeTab.value === 'bank') {
      return bankQuerySchema(companyOptions.value, currentCompany);
    } else if (activeTab.value === 'payroll') {
      return payrollQuerySchema(companyOptions.value, currentCompany);
    }
    return invoiceQuerySchema(
      companyOptions.value,
      currentCompany,
      activeTab.value as 'input' | 'output',
    );
  });

  // 数据源
  const tableData = ref<(BankReceiptData | InvoiceData | PayrollData)[]>([]);
  const loading = ref(false);

  // 更新场景相关状态
  const updateSceneModalVisible = ref(false);
  const updateBankReceiptSceneModalVisible = ref(false);
  const selectedInvoices = ref<InvoiceData[]>([]);
  const updateSceneLoading = ref(false);
  const selectedRowKeys = ref<string[]>([]);

  // PDF预览相关状态
  const pdfPreviewVisible = ref(false);
  const pdfPreviewUrl = ref('');
  const pdfPreviewTitle = ref('');

  // 表格行选择配置
  const rowSelection = computed(() => ({
    onChange: (keys: any[], rows: any[]) => {
      console.log('行选择变化:', keys, rows);
      selectedRowKeys.value = keys.map(String);
      selectedInvoices.value = rows;
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: any[],
      changeRows: any[],
    ) => {
      console.log('全选变化:', selected, selectedRows, changeRows);
    },
    selectedRowKeys: selectedRowKeys.value,
    type: 'checkbox' as const,
  }));

  // 全局公司状态管理
  const { companyList, fetchCompanyNames, selectedCompany } =
    useCompanySelection();

  // 公司选项
  const companyOptions = computed<CompanyOption[]>(() => {
    return companyList.value.map((company) => ({
      label: company.name,
      value: company.name,
    }));
  });

  // 用于强制刷新表格
  const tableKey = computed(() => `${activeTab.value}-table`);

  // 表单数据
  const formData = ref<Record<string, any>>({});

  // 重置表单数据
  const resetFormData = () => {
    const schema = formSchema.value;
    const newFormData: Record<string, any> = {};

    schema.forEach((item: any) => {
      // 如果是公司名称字段，优先使用全局选中的公司
      if (item.fieldName === 'company_name') {
        newFormData[item.fieldName] =
          selectedCompany.value || item.defaultValue;
      } else {
        newFormData[item.fieldName] =
          item.defaultValue === undefined ? undefined : item.defaultValue;
      }
    });

    formData.value = newFormData;
  };

  // 监听tab切换，重置表单数据
  watch(activeTab, () => {
    resetFormData();
  });

  // 监听公司选项变化，更新表单默认值
  watch(companyOptions, () => {
    resetFormData();
  });

  // Ant Design表格列配置
  const antdColumns = computed(() => {
    if (activeTab.value === 'bank') {
      return [
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'company_name',
          ellipsis: {
            showTitle: false,
          },
          key: 'company_name',
          minWidth: 160,
          title: '公司名称',
        },
        {
          dataIndex: 'transaction_time',
          key: 'transaction_time',
          minWidth: 150,
          title: '交易时间',
        },
        {
          dataIndex: 'month',
          key: 'month',
          minWidth: 80,
          title: '月份',
        },
        {
          dataIndex: 'type',
          key: 'type',
          minWidth: 80,
          title: '类型',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'scene',
          ellipsis: {
            showTitle: false,
          },
          key: 'scene',
          minWidth: 120,
          title: '场景',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'voucher_num',
          ellipsis: {
            showTitle: false,
          },
          key: 'voucher_num',
          minWidth: 120,
          title: '凭证编号',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'transaction_id',
          ellipsis: {
            showTitle: false,
          },
          key: 'transaction_id',
          minWidth: 150,
          title: '交易流水号',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'account_name',
          ellipsis: {
            showTitle: false,
          },
          key: 'account_name',
          minWidth: 160,
          title: '账户名称',
        },
        {
          dataIndex: 'bank_name',
          key: 'bank_name',
          minWidth: 120,
          title: '银行名称',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'conterpary_account_name',
          ellipsis: {
            showTitle: false,
          },
          key: 'conterpary_account_name',
          minWidth: 150,
          title: '对方账户名称',
        },
        {
          align: 'right' as const,
          dataIndex: 'amount',
          key: 'amount',
          minWidth: 120,
          title: '金额',
        },
        {
          dataIndex: 'currency',
          key: 'currency',
          minWidth: 60,
          title: '币种',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'summary',
          ellipsis: {
            showTitle: false,
          },
          key: 'summary',
          minWidth: 150,
          title: '摘要',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'note',
          ellipsis: {
            showTitle: false,
          },
          key: 'note',
          minWidth: 120,
          title: '备注',
        },
        {
          customRender: ({ record }: any) => {
            return h(
              Button,
              {
                onClick: () => handleViewOriginalFile(record),
                size: 'small',
                type: 'link',
              },
              () => '查看原文件',
            );
          },
          fixed: 'right' as const,
          key: 'action',
          title: '操作',
          width: 120,
        },
      ];
    } else if (activeTab.value === 'payroll') {
      // 工资单列配置
      return [
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'name',
          ellipsis: {
            showTitle: false,
          },
          key: 'name',
          minWidth: 100,
          title: '姓名',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'employee_id',
          ellipsis: {
            showTitle: false,
          },
          key: 'employee_id',
          minWidth: 120,
          title: '员工编号',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'id_number',
          ellipsis: {
            showTitle: false,
          },
          key: 'id_number',
          minWidth: 160,
          title: '身份证号',
        },
        {
          align: 'right' as const,
          dataIndex: 'basic_salary',
          key: 'basic_salary',
          minWidth: 100,
          title: '基本工资',
        },
        {
          align: 'right' as const,
          dataIndex: 'allowances',
          key: 'allowances',
          minWidth: 80,
          title: '津贴',
        },
        {
          align: 'right' as const,
          dataIndex: 'social_insurance_deduction',
          key: 'social_insurance_deduction',
          minWidth: 100,
          title: '社保扣除',
        },
        {
          align: 'right' as const,
          dataIndex: 'housing_fund_deduction',
          key: 'housing_fund_deduction',
          minWidth: 100,
          title: '公积金扣除',
        },
        {
          align: 'right' as const,
          dataIndex: 'income_tax',
          key: 'income_tax',
          minWidth: 100,
          title: '个人所得税',
        },
        {
          align: 'right' as const,
          dataIndex: 'net_salary',
          key: 'net_salary',
          minWidth: 100,
          title: '实发工资',
        },
        {
          align: 'center' as const,
          dataIndex: 'work_days',
          key: 'work_days',
          minWidth: 80,
          title: '工作天数',
        },
        {
          align: 'center' as const,
          dataIndex: 'overtime_hours',
          key: 'overtime_hours',
          minWidth: 80,
          title: '加班小时',
        },
        {
          dataIndex: 'month',
          key: 'month',
          minWidth: 80,
          title: '月份',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'remark',
          ellipsis: {
            showTitle: false,
          },
          key: 'remark',
          minWidth: 120,
          title: '备注',
        },
      ];
    } else {
      // 发票列配置
      return [
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'digital_invoice_number',
          ellipsis: {
            showTitle: false,
          },
          key: 'digital_invoice_number',
          minWidth: 160,
          title: '发票号码',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'goods_name',
          ellipsis: {
            showTitle: false,
          },
          key: 'goods_name',
          minWidth: 200,
          title: '货物或劳务明细',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'scene',
          ellipsis: {
            showTitle: false,
          },
          key: 'scene',
          minWidth: 120,
          title: 'AI记账场景',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'voucher_num',
          ellipsis: {
            showTitle: false,
          },
          key: 'voucher_num',
          minWidth: 120,
          title: '凭证号',
        },
        // 只在进项发票tab页显示销方名称列
        ...(activeTab.value === 'input'
          ? [
              {
                customRender: ({ text }: any) => {
                  return h(
                    'span',
                    {
                      title: text || '',
                    },
                    text || '-',
                  );
                },
                dataIndex: 'seller_name',
                ellipsis: {
                  showTitle: false,
                },
                key: 'seller_name',
                minWidth: 160,
                title: '销方名称',
              },
            ]
          : []),
        {
          dataIndex: 'issue_date',
          key: 'issue_date',
          minWidth: 120,
          title: '开票日期',
        },
        {
          dataIndex: 'type',
          key: 'type',
          minWidth: 120,
          title: '发票类型',
        },
        {
          customRender: ({ text }: any) => {
            return h(
              'span',
              {
                title: text || '',
              },
              text || '-',
            );
          },
          dataIndex: 'buyer_name',
          ellipsis: {
            showTitle: false,
          },
          key: 'buyer_name',
          minWidth: 160,
          title: '购方名称',
        },
        {
          align: 'right' as const,
          customRender: ({ text }: any) => {
            return text !== undefined && text !== null
              ? `¥${Number(text).toFixed(2)}`
              : '-';
          },
          dataIndex: 'total',
          key: 'total',
          minWidth: 120,
          title: '价税合计',
        },
        {
          align: 'right' as const,
          customRender: ({ text }: any) => {
            return text !== undefined && text !== null
              ? `¥${Number(text).toFixed(2)}`
              : '-';
          },
          dataIndex: 'total_tax',
          key: 'total_tax',
          minWidth: 100,
          title: '税额',
        },
        {
          align: 'center' as const,
          dataIndex: 'tax_rate',
          key: 'tax_rate',
          minWidth: 80,
          title: '税率',
        },
        {
          customRender: ({ text }: any) => {
            // 发票状态映射：0-正常，2-已作废，3-红冲
            const statusMap: Record<string, { color: string; text: string }> = {
              '0': { color: '#52c41a', text: '正常' },
              '2': { color: '#ff4d4f', text: '已作废' },
              '3': { color: '#fa8c16', text: '红冲' },
            };

            const status = statusMap[text] || {
              color: '#666',
              text: text || '-',
            };

            return h(
              'span',
              {
                style: {
                  color: status.color,
                  fontWeight: '500',
                },
              },
              status.text,
            );
          },
          dataIndex: 'status',
          key: 'status',
          minWidth: 100,
          title: '发票状态',
        },
        {
          customRender: ({ record }: any) => {
            return h(
              Button,
              {
                onClick: () => handleViewOriginalFile(record),
                size: 'small',
                type: 'link',
              },
              () => '查看原文件',
            );
          },
          fixed: 'right' as const,
          key: 'action',
          title: '操作',
          width: 120,
        },
      ];
    }
  });

  // 查询方法
  async function fetchData() {
    loading.value = true;
    try {
      const values = formData.value;

      if (activeTab.value === 'bank') {
        // 银行回单查询
        const [begin_time, end_time] = values.dateRange || [];

        // 处理月份格式转换
        let monthValue = values.month;
        if (monthValue && typeof monthValue === 'object' && monthValue.format) {
          // 如果是dayjs对象，转换为YYYYMM格式
          monthValue = monthValue.format('YYYYMM');
        }

        const params: BankReceiptQueryParams = {
          begin_time,
          company_name: values.company_name || '',
          end_time,
          month: monthValue,
          type: values.type,
          voucher_num: values.voucher_num,
        };

        // 如果仍然没有公司名称，提示用户
        if (!params.company_name) {
          message.warning('请选择公司名称');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach((key) => {
          const value = params[key as keyof BankReceiptQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof BankReceiptQueryParams];
          }
        });

        console.log('银行回单查询参数:', params);
        const result = await fetchBankReceiptList(params);

        if (result.success) {
          tableData.value = result.data || [];
          console.log(
            '获取银行回单数据成功:',
            result.data?.length || 0,
            '条记录',
          );
          console.log('银行回单数据:', result.data);

          // 打印原始响应用于调试
          if (result.originalResponse) {
            console.log('银行回单原始响应:', result.originalResponse);
          }
        } else {
          message.error(result.message || '获取银行回单数据失败');
          tableData.value = [];
        }
      } else if (activeTab.value === 'payroll') {
        // 工资单查询
        // 处理月份格式转换
        let monthValue = values.month || '';
        if (monthValue && typeof monthValue === 'object' && monthValue.format) {
          // 如果是dayjs对象，转换为YYYYMM格式
          monthValue = monthValue.format('YYYYMM');
        }

        const params: PayrollQueryParams = {
          company_name: values.company_name || '',
          id_number: values.id_number,
          month: monthValue,
          name: values.name,
          voucher_num: values.voucher_num,
        };

        // 如果仍然没有公司名称，提示用户
        if (!params.company_name) {
          message.warning('请选择公司名称');
          return;
        }

        // 如果没有月份，提示用户
        if (!params.month) {
          message.warning('请输入月份');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach((key) => {
          const value = params[key as keyof PayrollQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof PayrollQueryParams];
          }
        });

        console.log('工资单查询参数:', params);
        const result = await fetchPayrollList(params);

        if (result.success) {
          tableData.value = result.data || [];
          console.log(
            '获取工资单数据成功:',
            result.data?.length || 0,
            '条记录',
          );
          console.log('工资单数据:', result.data);

          // 打印原始响应用于调试
          if (result.originalResponse) {
            console.log('工资单原始响应:', result.originalResponse);
          }
        } else {
          message.error(result.message || '获取工资单数据失败');
          tableData.value = [];
        }
      } else {
        // 发票查询
        const [begin_time, end_time] = values.dateRange || [];
        const params: InvoiceQueryParams = {
          begin_time,
          company_name: values.company_name || '',
          end_time,
          input_output: activeTab.value as 'input' | 'output',
          status: values.status,
          voucher_num: values.voucher_num,
        };

        // 如果仍然没有公司名称，提示用户
        if (!params.company_name) {
          message.warning('请选择公司名称');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach((key) => {
          const value = params[key as keyof InvoiceQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof InvoiceQueryParams];
          }
        });

        console.log('发票查询参数:', params);
        const result = await fetchInvoiceList(params);

        if (result.success) {
          tableData.value = result.data || [];
          console.log('获取发票数据成功:', result.data?.length || 0, '条记录');
          console.log('发票数据:', result.data);

          // 打印原始响应用于调试
          if (result.originalResponse) {
            console.log('发票原始响应:', result.originalResponse);
          }
        } else {
          message.error(result.message || '获取发票数据失败');
          tableData.value = [];
        }
      }
    } catch (error: any) {
      console.error('请求失败:', error);
      message.error(error?.message || '请求失败');
      tableData.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 设置默认选中的公司
  function setDefaultSelectedCompany() {
    const currentSelected = selectedCompany.value;
    if (currentSelected) {
      formData.value.company_name = currentSelected;
      console.log('设置默认选中公司:', currentSelected);
    }
  }

  // 页面初始化时获取公司列表和数据
  onMounted(async () => {
    try {
      // 先获取公司列表
      await fetchCompanyNames();
      console.log('公司列表获取完成:', companyList.value);

      // 等待一小段时间确保状态更新
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 设置默认选中的公司
      setDefaultSelectedCompany();

      // 初始化表单数据
      resetFormData();

      // 然后获取数据
      await fetchData();
    } catch (error) {
      console.error('页面初始化失败:', error);
      // 即使获取公司列表失败，也尝试获取数据
      setDefaultSelectedCompany();
      resetFormData();
      fetchData();
    }

    // 添加数据刷新事件监听器
    const handleReloadData = () => {
      console.log('收到刷新原始凭证数据事件');
      fetchData();
    };

    window.addEventListener('reload-original-voucher-data', handleReloadData);

    // 组件卸载时移除事件监听器
    onUnmounted(() => {
      window.removeEventListener(
        'reload-original-voucher-data',
        handleReloadData,
      );
    });
  });

  // tab切换时自动刷新数据
  watch(activeTab, () => {
    // 清空选中状态
    clearSelectedInvoices();
    fetchData();
  });

  // 监听表单中公司选择变化
  watch(
    () => formData.value.company_name,
    (newCompany) => {
      console.log('表单公司选择变化:', newCompany);
      // 清空选中状态
      clearSelectedInvoices();
      // 同步到全局选中公司
      if (newCompany && newCompany !== selectedCompany.value) {
        selectedCompany.value = newCompany;
      }
      // 当公司变化时，自动刷新数据
      if (newCompany) {
        fetchData();
      }
    },
  );

  // 监听表格数据变化，清空选中状态
  watch(tableData, () => {
    clearSelectedInvoices();
  });

  // 监听全局选中公司变化，同步到表单
  watch(
    selectedCompany,
    (newCompany: string) => {
      if (newCompany && newCompany !== formData.value.company_name) {
        console.log('同步全局选中公司到表单:', newCompany);
        formData.value.company_name = newCompany;
        // 公司变化时自动刷新数据
        fetchData();
      }
    },
    { immediate: true },
  );

  // 处理表单字段变化
  function handleFieldChange(fieldName: string, value: any) {
    console.log('表单字段变化:', fieldName, value);

    // 如果是公司名称字段变化，同步到全局选中公司
    if (
      fieldName === 'company_name' &&
      value &&
      value !== selectedCompany.value
    ) {
      selectedCompany.value = value;
    }
  }

  // 搜索按钮点击事件
  function handleSearch() {
    fetchData();
  }

  // 重置按钮点击事件
  function handleReset() {
    console.log('重置表单和选中状态');

    // 重置表单数据
    resetFormData();

    // 清空选中状态
    clearSelectedInvoices();

    // 重新获取数据
    fetchData();

    message.success('已重置搜索条件');
  }

  // 操作按钮
  function handleAdd() {
    console.log('点击同步数据按钮，根据当前标签传递不同的data_types');

    // 根据当前标签确定data_types
    let dataTypes: string[] = [];
    switch (activeTab.value) {
      case 'input': {
        dataTypes = ['一般进项', '进项专票'];
        break;
      }
      case 'output': {
        dataTypes = ['销项发票'];
        break;
      }
      default: {
        dataTypes = ['销项发票', '一般进项', '进项专票'];
        break;
      }
    }

    // 通过自定义事件触发AI助手按钮点击
    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'sync-data',
        dataTypes, // 传递data_types参数
        message: '请同步当前公司的最新数据',
      },
    });

    window.dispatchEvent(aiToggleEvent);
  }

  function handleAI() {
    console.log('点击AI记账按钮，根据当前标签发送db_to_voucher_request消息');

    // 只有发票标签页才支持AI记账
    if (activeTab.value !== 'input' && activeTab.value !== 'output') {
      console.warn('AI记账功能仅支持发票标签页');
      return;
    }

    // 根据当前标签确定types
    let types: string[] = [];
    switch (activeTab.value) {
      case 'input': {
        types = ['进项专票', '一般进项'];
        break;
      }
      case 'output': {
        types = ['销项发票'];
        break;
      }
      default: {
        // 这个分支现在不应该被执行到
        console.warn('未知的标签页类型:', activeTab.value);
        return;
      }
    }

    // 通过自定义事件触发AI助手发送db_to_voucher_request消息
    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'db-to-voucher',
        message: 'AI记账(资金场景识别)',
        types, // 传递types参数
      },
    });

    window.dispatchEvent(aiToggleEvent);
  }

  // 更新场景按钮点击事件
  function handleUpdateScene() {
    console.log('更新场景按钮被点击');
    console.log('当前活动标签:', activeTab.value);

    if (
      activeTab.value !== 'input' &&
      activeTab.value !== 'output' &&
      activeTab.value !== 'bank'
    ) {
      message.warning('只有发票和银行回单数据支持更新场景功能');
      return;
    }

    const selectedRows = getSelectedRows();
    console.log('选中的行数据:', selectedRows);

    if (selectedRows.length === 0) {
      const dataType = activeTab.value === 'bank' ? '银行回单' : '发票';
      message.warning(`请先选择要更新场景的${dataType}`);
      return;
    }

    selectedInvoices.value = selectedRows as InvoiceData[];
    console.log('设置选中的数据:', selectedInvoices.value);
    console.log('准备打开模态框');

    // 根据不同的tab打开不同的模态框
    if (activeTab.value === 'bank') {
      updateBankReceiptSceneModalVisible.value = true;
      console.log(
        '银行回单模态框可见状态:',
        updateBankReceiptSceneModalVisible.value,
      );
    } else {
      updateSceneModalVisible.value = true;
      console.log('发票模态框可见状态:', updateSceneModalVisible.value);
    }
  }

  // 获取选中的行
  function getSelectedRows() {
    console.log('当前选中的发票:', selectedInvoices.value);
    console.log('当前选中的行键:', selectedRowKeys.value);
    return selectedInvoices.value;
  }

  // 批量更新发票场景
  async function batchUpdateInvoiceScene(sceneData: any) {
    if (selectedInvoices.value.length === 0) {
      message.warning('没有选中的发票');
      return;
    }

    updateSceneLoading.value = true;
    try {
      const updateParams: InvoiceUpdateSceneParams[] =
        selectedInvoices.value.map((invoice) => ({
          company_name: formData.value.company_name || '',
          id: invoice._id,
          ii_buyer: sceneData.ii_buyer || '',
          ii_goods: sceneData.ii_goods || '',
          ii_note: sceneData.ii_note || '',
          ii_seller: sceneData.ii_seller || '',
          ii_tax_rates: sceneData.ii_tax_rates || 0,
          ii_type: sceneData.ii_type || 'normal',
          scene: sceneData.scene,
          type:
            activeTab.value === 'input' ? 'input_invoice' : 'output_invoice',
        }));

      const result = await updateInvoiceScene(updateParams);

      if (result.success) {
        message.success(`成功更新 ${selectedInvoices.value.length} 条发票场景`);
        updateSceneModalVisible.value = false;
        // 清空选中状态
        clearSelectedInvoices();
        // 刷新数据
        await fetchData();
      } else {
        message.error(result.message || '更新发票场景失败');
      }
    } catch (error: any) {
      console.error('更新发票场景失败:', error);
      message.error(error?.message || '更新发票场景失败');
    } finally {
      updateSceneLoading.value = false;
    }
  }

  // 批量更新银行回单场景
  async function batchUpdateBankReceiptScene(sceneData: any) {
    if (selectedInvoices.value.length === 0) {
      message.warning('没有选中的银行回单');
      return;
    }

    updateSceneLoading.value = true;
    try {
      // 使用正确的类型定义
      const updateParams: BankReceiptUpdateSceneParams[] =
        selectedInvoices.value.map((bankReceipt) => ({
          br_account_name: sceneData.br_account_name || '',
          br_conterpary_account_name:
            sceneData.br_conterpary_account_name || '',
          br_currency: sceneData.br_currency || 'CNY',
          br_note: sceneData.br_note || '',
          br_summary: sceneData.br_summary || '',
          br_type: sceneData.br_type || '',
          company_name: formData.value.company_name || '',
          id: bankReceipt._id,
          scene: sceneData.scene,
          type: 'bank_receipt' as const,
        }));

      // 调用银行回单更新场景API
      console.log('银行回单更新参数:', updateParams);

      // 使用传统的静态导入方法
      const result = await updateBankReceiptScene(updateParams);

      if (result.success) {
        message.success(
          `成功更新 ${selectedInvoices.value.length} 条银行回单场景`,
        );
        updateBankReceiptSceneModalVisible.value = false;
        // 清空选中状态
        clearSelectedInvoices();
        // 刷新数据
        await fetchData();
      } else {
        message.error('更新银行回单场景失败');
      }
    } catch (error: any) {
      console.error('更新银行回单场景失败:', error);
      message.error(error?.message || '更新银行回单场景失败');
    } finally {
      updateSceneLoading.value = false;
    }
  }

  // 处理更新场景模态框取消事件
  function handleUpdateSceneCancel() {
    console.log('取消更新场景，清空选中状态');
    clearSelectedInvoices();
  }

  // 处理银行回单更新场景模态框取消事件
  function handleUpdateBankReceiptSceneCancel() {
    console.log('取消银行回单更新场景，清空选中状态');
    clearSelectedInvoices();
  }

  // 清空选中的发票状态
  function clearSelectedInvoices() {
    selectedInvoices.value = [];
    selectedRowKeys.value = [];
    console.log('已清空选中状态');
  }

  // 构建文件URL（参考其他页面的实现）
  function buildFileUrl(row: any) {
    // 如果有url字段，直接使用
    if (row.url) {
      return row.url;
    }

    // 如果有digital_invoice_number字段，直接拼接在URL最后面
    if (row.digital_invoice_number) {
      const baseUrl = 'http://**************:30081/prod-api/autojob/files';
      const companyName = row.company_name || formData.value.company_name || '';
      const encodedCompanyName = encodeURIComponent(companyName);

      return `${baseUrl}/${encodedCompanyName}/${row.month}/${row.digital_invoice_number}.pdf`;
    }

    // 如果有source_file字段，构建完整URL
    if (row.source_file) {
      const baseUrl = 'http://**************:30081/prod-api/autojob/files';
      const companyName = row.company_name || formData.value.company_name || '';
      const encodedCompanyName = encodeURIComponent(companyName);

      // 根据数据类型判断是否需要添加月份路径
      const isBankReceipt = activeTab.value === 'bank';
      if (isBankReceipt && row.month) {
        return `${baseUrl}/${encodedCompanyName}/${row.month}/${row.source_file}`;
      }

      return `${baseUrl}/${encodedCompanyName}/${row.source_file}`;
    }

    // 默认返回空链接
    return '#';
  }

  // 查看原文件
  async function handleViewOriginalFile(record: any) {
    // 构建文件URL
    const fileUrl = buildFileUrl(record);
    if (fileUrl === '#') {
      message.warning('该记录没有关联的原文件');
      return;
    }

    // 使用模态框预览原文件
    pdfPreviewUrl.value = fileUrl;
    pdfPreviewTitle.value = `原文件 - ${record.company_name || '未知公司'}`;
    pdfPreviewVisible.value = true;
  }

  // 关闭PDF预览
  function closePdfPreview() {
    pdfPreviewVisible.value = false;
    pdfPreviewUrl.value = '';
    pdfPreviewTitle.value = '';
  }
</script>

<template>
  <div class="p-4">
    <a-card
      class="voucher-card"
      :body-style="{ padding: '20px 20px 12px 20px' }"
    >
      <div class="voucher-header">
        <a-tabs v-model:active-key="activeTab" class="voucher-tabs">
          <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.label" />
        </a-tabs>
      </div>
      <div class="voucher-filter-section">
        <FilterForm
          :schema="formSchema"
          v-model="formData"
          @submit="handleSearch"
          @field-change="handleFieldChange"
        />
        <div class="voucher-filter-actions">
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button class="reset-btn ml-2" @click="handleReset">重置</a-button>
          <a-button
            v-if="activeTab === 'input' || activeTab === 'output'"
            class="ml-2"
            @click="handleAdd"
          >
            同步数据
          </a-button>
          <a-button
            v-if="activeTab === 'input' || activeTab === 'output'"
            class="ai-btn ml-2"
            @click="handleAI"
          >
            AI记账
          </a-button>
          <a-button
            v-if="
              activeTab === 'input' ||
              activeTab === 'output' ||
              activeTab === 'bank'
            "
            class="ml-2"
            @click="handleUpdateScene"
          >
            更新场景
          </a-button>
        </div>
      </div>
      <div class="voucher-table-wrap">
        <a-table
          :key="tableKey"
          :columns="antdColumns"
          :data-source="tableData"
          :loading="loading"
          :pagination="{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }"
          :row-selection="
            activeTab === 'input' ||
            activeTab === 'output' ||
            activeTab === 'bank'
              ? rowSelection
              : undefined
          "
          :scroll="{ x: 'max-content' }"
          row-key="_id"
          size="small"
        />
      </div>

      <!-- 发票更新场景模态框 -->
      <UpdateSceneModal
        v-model:visible="updateSceneModalVisible"
        :loading="updateSceneLoading"
        :selected-count="selectedInvoices.length"
        :company-name="formData.company_name"
        @confirm="batchUpdateInvoiceScene"
        @cancel="handleUpdateSceneCancel"
      />

      <!-- 银行回单更新场景模态框 -->
      <UpdateBankReceiptSceneModal
        v-model:visible="updateBankReceiptSceneModalVisible"
        :loading="updateSceneLoading"
        :selected-count="selectedInvoices.length"
        :company-name="formData.company_name"
        @confirm="batchUpdateBankReceiptScene"
        @cancel="handleUpdateBankReceiptSceneCancel"
      />

      <!-- PDF预览模态框 -->
      <a-modal
        v-model:open="pdfPreviewVisible"
        :title="pdfPreviewTitle"
        width="80%"
        :footer="null"
        :centered="true"
        @cancel="closePdfPreview"
      >
        <div class="pdf-preview-container">
          <iframe
            v-if="pdfPreviewUrl"
            :src="pdfPreviewUrl"
            class="pdf-preview-iframe"
            frameborder="0"
          ></iframe>
        </div>
      </a-modal>
    </a-card>
  </div>
</template>

<style scoped>
  /* 响应式布局优化 */
  @media (max-width: 1200px) {
    .voucher-filter-actions {
      justify-content: center;
    }
  }

  @media (max-width: 768px) {
    .voucher-filter-section {
      padding: 16px;
    }

    .voucher-filter-actions {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;
    }

    .voucher-filter-actions .ant-btn {
      width: 100%;
    }
  }

  .voucher-card {
    margin: 0 auto;
    overflow: hidden;
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgb(0 0 0 / 8%);
  }

  .voucher-header {
    margin-bottom: 12px;
  }

  .voucher-tabs {
    font-size: 16px;
  }

  :deep(.voucher-tabs .ant-tabs-tab) {
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
  }

  :deep(.voucher-tabs .ant-tabs-tab:hover) {
    color: #4f8cff;
  }

  :deep(.voucher-tabs .ant-tabs-tab-active) {
    font-weight: 600;
    color: #4f8cff !important;
  }

  :deep(.voucher-tabs .ant-tabs-ink-bar) {
    height: 3px;
    background: linear-gradient(90deg, #4f8cff 0%, #6ad1ff 100%);
    border-radius: 2px;
  }

  .voucher-filter-section {
    padding: 24px;
    margin-bottom: 16px;
    overflow: hidden; /* 防止内容溢出 */
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border: 1px solid #e8eaed;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  }

  .voucher-filter-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: flex-end;
    padding-top: 16px;
    margin-top: 20px;
    border-top: 1px solid #e8eaed;
  }

  .ai-btn {
    font-weight: 500;
    color: #fff;
    background: linear-gradient(135deg, #4f8cff 0%, #6ad1ff 100%);
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(79 140 255 / 30%);
    transition: all 0.3s ease;
  }

  .ai-btn:hover {
    background: linear-gradient(135deg, #3d7bff 0%, #5bc4ff 100%);
    box-shadow: 0 4px 12px rgb(79 140 255 / 40%);
    transform: translateY(-1px);
  }

  /* 重置按钮样式 */
  .reset-btn {
    color: #666 !important;
    background: #fff !important;
    border-color: #d9d9d9 !important;
  }

  .reset-btn:hover {
    color: #ff7875 !important;
    background: #fff2f0 !important;
    border-color: #ff7875 !important;
    transform: translateY(-1px);
  }

  /* 按钮样式优化 */
  :deep(.ant-btn) {
    font-size: 13px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  :deep(.ant-btn-primary) {
    background: #4f8cff;
    border-color: #4f8cff;
    box-shadow: 0 2px 6px rgb(79 140 255 / 20%);
  }

  :deep(.ant-btn-primary:hover) {
    background: #3d7bff;
    border-color: #3d7bff;
    box-shadow: 0 4px 10px rgb(79 140 255 / 30%);
    transform: translateY(-1px);
  }

  :deep(.ant-btn-default) {
    color: #666;
    border-color: #d9d9d9;
  }

  :deep(.ant-btn-default:hover) {
    color: #4f8cff;
    border-color: #4f8cff;
    transform: translateY(-1px);
  }

  .voucher-table-wrap {
    margin-top: 8px;
  }

  /* PDF预览样式 */
  .pdf-preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 70vh;
  }

  .pdf-preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  :deep(.ant-table) {
    overflow: hidden;
    font-size: 13px;
    border-radius: 12px;
  }

  :deep(.ant-table-thead > tr > th) {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-bottom: 2px solid #e8eaed;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f8f9ff;
  }

  /* 表格内容自适应优化 */
  :deep(.ant-table-tbody > tr > td .ant-table-cell-ellipsis) {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
  }

  /* 长文本列的特殊处理 */
  :deep(.ant-table-tbody > tr > td[data-index='goods_name']),
  :deep(.ant-table-tbody > tr > td[data-index='digital_invoice_number']),
  :deep(.ant-table-tbody > tr > td[data-index='company_name']),
  :deep(.ant-table-tbody > tr > td[data-index='seller_name']),
  :deep(.ant-table-tbody > tr > td[data-index='buyer_name']) {
    min-width: 120px;
    max-width: 200px;
  }

  /* 表格容器滚动优化 */
  :deep(.ant-table-container) {
    overflow-x: auto;
  }

  :deep(.ant-table-body) {
    overflow: auto;
  }

  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }

  :deep(.ant-form-item-label) {
    font-size: 13px;
  }

  :deep(.ant-input) {
    width: 100% !important;
    font-size: 13px;
  }

  :deep(.ant-select) {
    width: 100% !important;
    font-size: 13px;
  }

  /* 表单字段样式优化 */
  :deep(.ant-form-item-control-input) {
    min-height: 32px;
  }

  :deep(.ant-picker) {
    width: 100% !important;
  }
</style>

// 记账凭证相关类型定义

export interface ListItm {
  id?: string;
  abstract?: {
    id: string;
    text: string;
  } | string;
  subject?: {
    id: string;
    name: string;
    code: string;
    text: string;
    useAssistant?: boolean;
    assistantType?: string;
    assistantOptions?: Array<{
      id: string;
      name: string;
      code: string;
    }>;
    balance?: number;
  };
  borrower?: string; // 借方金额
  lender?: string;   // 贷方金额
  balance?: number;  // 余额
  auxiliary?: {
    id: string;
    name: string;
  };
  currency?: {
    money: string;
    rate: string;
    currencyCode: string;
  };
}

export interface VoucherState {
  list: ListItm[];
  borrowerAll: string;
  lenderAll: string;
}

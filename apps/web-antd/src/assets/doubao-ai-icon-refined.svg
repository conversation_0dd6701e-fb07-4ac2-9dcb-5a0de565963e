<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2B5AED" />
      <stop offset="100%" stop-color="#1677FF" />
    </linearGradient>
    
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0,0,0,0.2)" />
    </filter>
    
    <linearGradient id="beanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="100%" stop-color="#F5F5F7" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)" />
  
  <!-- 内部豆包形状 -->
  <path d="M100 45 C135 45, 155 70, 155 100 C155 135, 130 155, 100 155 C70 155, 45 130, 45 100 C45 70, 65 45, 100 45 Z" 
        fill="url(#beanGradient)" />
  
  <!-- 豆包内部眼睛 -->
  <g>
    <circle cx="80" cy="90" r="10" fill="#1677FF" />
    <circle cx="82" cy="87" r="3" fill="white" />
  </g>
  
  <g>
    <circle cx="120" cy="90" r="10" fill="#1677FF" />
    <circle cx="122" cy="87" r="3" fill="white" />
  </g>
  
  <!-- 豆包笑脸 -->
  <path d="M80 115 Q100 135 120 115" stroke="#1677FF" stroke-width="6" stroke-linecap="round" fill="none" />
  
  <!-- 顶部光晕效果 -->
  <circle cx="75" cy="65" r="12" fill="white" opacity="0.4" />
  
  <!-- AI元素 - 电路图案 -->
  <g opacity="0.7">
    <line x1="30" y1="100" x2="45" y2="100" stroke="#FFFFFF" stroke-width="2" />
    <line x1="155" y1="100" x2="170" y2="100" stroke="#FFFFFF" stroke-width="2" />
    <line x1="100" y1="30" x2="100" y2="45" stroke="#FFFFFF" stroke-width="2" />
    <line x1="100" y1="155" x2="100" y2="170" stroke="#FFFFFF" stroke-width="2" />
    
    <circle cx="30" cy="100" r="3" fill="#FFFFFF" />
    <circle cx="170" cy="100" r="3" fill="#FFFFFF" />
    <circle cx="100" cy="30" r="3" fill="#FFFFFF" />
    <circle cx="100" cy="170" r="3" fill="#FFFFFF" />
  </g>
  
  <!-- 脉冲动画效果 -->
  <circle cx="100" cy="100" r="95" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.2">
    <animate attributeName="r" values="90;100;90" dur="2s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.3;0.1" dur="2s" repeatCount="indefinite" />
  </circle>
</svg>

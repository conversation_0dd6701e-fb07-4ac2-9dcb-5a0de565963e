import type { RouteRecordStringComponent } from '@vben/types';

import { $t } from '@vben/locales';

// const {
//   version,
//   // vite inject-metadata 插件注入的全局变量
// } = __VBEN_ADMIN_METADATA__ || {};

/**
 * 该文件放非后台返回的路由 比如个人中心 等需要跳转显示的页面
 */
const localRoutes: RouteRecordStringComponent[] = [
  {
    component: '/_core/profile/index',
    meta: {
      icon: 'mingcute:profile-line',
      title: '个人中心',
      hideInMenu: true,
      requireHomeRedirect: true,
    },
    name: 'Profile',
    path: '/profile',
  },
];

/**
 * 简化的本地路由菜单
 */
export const localMenuList: RouteRecordStringComponent[] = [
  {
    component: 'BasicLayout',
    meta: {
      order: 1,
      title: '首页',
      noBasicLayout: true,
    },
    name: 'Dashboard',
    path: '/',
    redirect: '/analytics',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: '/jsj-ai/dashboard/analytics/index',
        meta: {
          affixTab: true,
          icon: 'lucide:building-2',
          title: '公司状态',
        },
      },
    ],
  },

  {
    component: 'BasicLayout',
    meta: {
      icon: 'ant-design:file-text-outlined',
      order: 2,
      title: '记账凭证',
    },
    name: 'bookkeeping',
    path: '/bookkeeping',
    children: [
      // {
      //   name: 'VoucherOverview2',
      //   path: '/accountingVouchers/voucher-overview2',
      //   component: '/jsj/voucher-overview2/index',
      //   meta: {
      //     icon: 'lucide:file-text',
      //     title: 'AI凭证',
      //   },
      // },
      // {
      //   name: 'bookkeeping-enter',
      //   path: '/bookkeeping/enter',
      //   component: '/jsj-ai/voucher/bookkeeping/enter/index',
      //   meta: {
      //     icon: 'ant-design:edit-outlined',
      //     title: '录入凭证',
      //   },
      // },
      {
        name: 'voucher-view',
        path: '/bookkeeping/view',
        component: '/jsj-ai/voucher/bookkeeping/view/index',
        meta: {
          icon: 'ant-design:file-search-outlined',
          title: '查看凭证',
        },
      },
      {
        name: 'voucher-review',
        path: '/bookkeeping/review',
        component: '/jsj-ai/voucher/bookkeeping/review/index',
        meta: {
          icon: 'ant-design:audit-outlined',
          title: '凭证审核',
          hideInMenu: true,
        },
      },
      {
        name: 'voucher-validation-test',
        path: '/bookkeeping/test/validation',
        component: '/jsj-ai/voucher/bookkeeping/test/validation-test',
        meta: {
          icon: 'ant-design:bug-outlined',
          title: '校验测试',
          hideInMenu: true, // 隐藏在菜单中，仅用于测试
        },
      },


    ],
  },

  {
    component: 'BasicLayout',
    meta: {
      icon: 'lucide:file-text',
      order: 3,
      title: '凭证管理',
    },
    name: 'voucher-management',
    path: '/voucher',
    children: [
      {
        name: 'original-voucher',
        path: '/voucher/original',
        component: '/jsj-ai/voucher/original/index',
        meta: {
          icon: 'ant-design:file-image-outlined',
          title: '原始凭证',
        },
      },
    ],
  },

  {
    component: 'BasicLayout',
    meta: {
      icon: 'ant-design:setting-outlined',
      order: 4,
      title: '配置',
    },
    name: 'configuration',
    path: '/configuration',
    children: [
      {
        name: 'scenario-condition',
        path: '/configuration/scenario-condition',
        component: '/jsj-ai/configuration/scenario-condition/index',
        meta: {
          icon: 'ant-design:setting-outlined',
          title: '场景条件配置',
        },
      },
      {
        name: 'scenario-entry',
        path: '/configuration/scenario-entry',
        component: '/jsj-ai/configuration/scenario-entry/index',
        meta: {
          icon: 'ant-design:form-outlined',
          title: '场景分录配置',
        },
      },
    ],
  },

  ...localRoutes,
];

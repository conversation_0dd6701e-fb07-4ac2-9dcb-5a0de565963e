// @ts-check

import { defineConfig } from '@vben/eslint-config';

export default defineConfig([
  // 自定义覆盖规则
  {
    files: ['**/*.vue'],
    rules: {
      // 关闭HTML标签换行检查，避免格式化冲突
      'vue/html-closing-bracket-newline': 'off',
      // 允许更灵活的HTML缩进
      'vue/html-indent': 'off',
      // 允许更灵活的属性换行
      'vue/max-attributes-per-line': 'off',
      // 允许单行元素内容不换行
      'vue/singleline-html-element-content-newline': 'off',
      // 允许多行元素内容换行更灵活
      'vue/multiline-html-element-content-newline': 'off',
      // 允许console.log用于调试
      'no-console': ['warn', { allow: ['warn', 'error', 'log'] }],
    },
  },
]);

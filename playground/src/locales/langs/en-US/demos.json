{"title": "Demos", "access": {"frontendPermissions": "Frontend Permissions", "backendPermissions": "Backend Permissions", "pageAccess": "Page Access", "buttonControl": "Button Control", "menuVisible403": "Menu Visible(403)", "superVisible": "Visible to Super", "adminVisible": "Visible to Admin", "userVisible": "Visible to User"}, "nested": {"title": "Nested Menu", "menu1": "Menu 1", "menu2": "Menu 2", "menu2_1": "Menu 2-1", "menu3": "Menu 3", "menu3_1": "Menu 3-1", "menu3_2": "Menu 3-2", "menu3_2_1": "Menu 3-2-1"}, "outside": {"title": "External Pages", "embedded": "Embedded", "externalLink": "External Link"}, "badge": {"title": "<PERSON><PERSON>", "dot": "Dot Badge", "text": "Text Badge", "color": "Badge Color"}, "activeIcon": {"title": "Active Menu Icon", "children": "Children Active Icon"}, "fallback": {"title": "Fallback <PERSON>"}, "features": {"title": "Features", "hideChildrenInMenu": "Hide Menu Children", "loginExpired": "Login Expired", "icons": "Icons", "watermark": "Watermark", "tabs": "Tabs", "tabDetail": "<PERSON>b Detail Page", "fullScreen": "FullScreen", "clipboard": "Clipboard", "menuWithQuery": "Menu With Query", "openInNewWindow": "Open in New Window", "fileDownload": "File Download"}, "breadcrumb": {"navigation": "Breadcrumb Navigation", "lateral": "Lateral Mode", "lateralDetail": "Lateral Mode Detail", "level": "Level Mode", "levelDetail": "Level Mode Detail"}, "vben": {"title": "Project", "about": "About", "document": "Document", "antdv": "Ant Design Vue Version", "naive-ui": "Naive UI Version", "element-plus": "Element Plus Version"}}
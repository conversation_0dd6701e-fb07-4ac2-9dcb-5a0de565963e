<script setup lang="ts">
import { computed } from 'vue';

import { Bot } from '@vben-core/icons';
import { VbenButton } from '@vben-core/shadcn-ui';

interface Props {
  collapsed?: boolean;
  theme?: 'dark' | 'light';
  visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  theme: 'dark',
  visible: false,
});

const emit = defineEmits<{
  toggle: [];
  'update:visible': [value: boolean];
}>();

const buttonClass = computed(() => {
  const classes = [
    'w-full flex items-center justify-center transition-all duration-200',
    'active:scale-95',
  ];

  if (props.collapsed) {
    classes.push('h-12 rounded-lg');
  } else {
    classes.push('h-10 rounded-md gap-2');
  }

  if (props.visible) {
    classes.push('bg-primary text-primary-foreground shadow-md');
  } else {
    if (props.theme === 'dark') {
      classes.push(
        'bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground',
      );
    } else {
      classes.push(
        'bg-background hover:bg-accent text-foreground hover:text-accent-foreground border',
      );
    }
  }

  return classes.join(' ');
});

const iconClass = computed(() => {
  const classes = ['transition-transform duration-200'];

  if (props.collapsed) {
    classes.push('size-6');
  } else {
    classes.push('size-4');
  }

  if (props.visible) {
    classes.push('animate-pulse');
  }

  return classes.join(' ');
});

function handleClick() {
  const newVisible = !props.visible;
  console.log('AI助手按钮被点击，当前状态:', props.visible, '新状态:', newVisible);
  emit('update:visible', newVisible);
  emit('toggle');
}
</script>

<template>
  <VbenButton
    :class="buttonClass"
    variant="ghost"
    size="sm"
    @click="handleClick"
  >
    <Bot :class="iconClass" />
    <span
      v-if="!collapsed"
      class="text-sm font-medium transition-opacity duration-200"
      :class="{ 'opacity-90': !visible, 'opacity-100': visible }"
    >
      AI 助手
    </span>

    <!-- 活跃指示器 -->
    <div
      v-if="visible && !collapsed"
      class="ml-auto h-2 w-2 animate-pulse rounded-full bg-green-400"
    ></div>
  </VbenButton>
</template>

<style scoped>
@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgb(59 130 246 / 50%);
  }

  50% {
    box-shadow: 0 0 20px rgb(59 130 246 / 80%);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
</style>

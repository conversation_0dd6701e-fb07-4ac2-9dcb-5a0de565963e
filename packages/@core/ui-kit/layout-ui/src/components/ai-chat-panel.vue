<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';

import { Bot, Loader2, Paperclip, Send, User } from '@vben-core/icons';
import {
  VbenButton,
  Input as VbenInput,
  VbenScrollbar,
} from '@vben-core/shadcn-ui';

interface ChatMessage {
  content: string;
  files?: File[];
  id: string;
  timestamp: Date;
  type: 'ai' | 'system' | 'user';
}

interface QuickAction {
  action: string;
  id: string;
  label: string;
  menuPath?: string;
}

interface Props {
  sidebarWidth?: number;
  visible?: boolean;
  width?: number;
}

const props = withDefaults(defineProps<Props>(), {
  sidebarWidth: 0,
  visible: false,
  width: 400,
});

const emit = defineEmits<{
  'navigate-to': [path: string, params?: Record<string, any>];
  'quick-action': [action: QuickAction];
  'send-message': [message: string, files?: File[]];
  'update:visible': [value: boolean];
}>();

// 响应式数据
const inputValue = ref('');
const isLoading = ref(false);
const messages = ref<ChatMessage[]>([
  {
    content: '欢迎使用AI助手！您可以拖拽文件到输入框或使用快捷按钮进行交互。',
    id: '1',
    timestamp: new Date(),
    type: 'system',
  },
]);
const dragOver = ref(false);
const fileInputRef = ref<HTMLInputElement>();
const chatContainerRef = ref<HTMLElement>();
const selectedFiles = ref<File[]>([]);

// 快捷操作按钮配置
const quickActions = ref<QuickAction[]>([
  {
    action: 'analyze_data',
    id: '1',
    label: '采集',
    menuPath: '/analytics',
  },
  {
    action: 'generate_voucher',
    id: '2',
    label: '生成凭证',
    menuPath: '/bookkeeping/view',
  },
  {
    action: 'depreciation',
    id: '3',
    label: '折旧',
    menuPath: '/original-voucher',
  },
  {
    action: 'carry_forward',
    id: '4',
    label: '结转',
    menuPath: '/bookkeeping/enter',
  },
  {
    action: 'inventory',
    id: '5',
    label: '库存',
    menuPath: '/bookkeeping/view',
  },
  {
    action: 'wechat',
    id: '6',
    label: '微信',
    menuPath: '/original-voucher',
  },
  {
    action: 'write_voucher',
    id: '7',
    label: '写入凭证',
    menuPath: '/bookkeeping/enter',
  },
  {
    action: 'report',
    id: '8',
    label: '报表',
    menuPath: '/analytics',
  },
  {
    action: 'tax_report',
    id: '9',
    label: '报税',
    menuPath: '/analytics',
  },
  {
    action: 'closing',
    id: '10',
    label: '结账',
    menuPath: '/bookkeeping/view',
  },
]);

// 计算属性
const panelStyle = computed(() => ({
  height: 'calc(100% - 50px)',
  left: `${props.sidebarWidth}px`,
  top: '50px',
  transform: props.visible ? 'translateX(0)' : `translateX(-${props.width}px)`,
  width: `${props.width}px`,
}));

const hasFiles = computed(() => selectedFiles.value.length > 0);

// 方法
function addMessage(
  type: ChatMessage['type'],
  content: string,
  files?: File[],
) {
  const message: ChatMessage = {
    content,
    files,
    id: Date.now().toString(),
    timestamp: new Date(),
    type,
  };
  messages.value.push(message);
  nextTick(() => {
    scrollToBottom();
  });
}

function scrollToBottom() {
  if (chatContainerRef.value) {
    const scrollContainer = chatContainerRef.value.querySelector(
      '.scrollbar-container',
    );
    if (scrollContainer) {
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }
}

async function sendMessage() {
  if (!inputValue.value.trim() && selectedFiles.value.length === 0) return;

  const messageContent = inputValue.value.trim();
  const files = [...selectedFiles.value];

  // 添加用户消息
  addMessage('user', messageContent, files.length > 0 ? files : undefined);

  // 清空输入
  inputValue.value = '';
  selectedFiles.value = [];

  // 设置加载状态
  isLoading.value = true;

  try {
    // 发送消息到后端
    emit('send-message', messageContent, files.length > 0 ? files : undefined);
  } catch (error) {
    console.error('发送消息失败:', error);
    addMessage('system', '发送消息失败，请重试。');
  } finally {
    isLoading.value = false;
  }
}

function handleQuickAction(action: QuickAction) {
  // 发送快捷操作消息
  addMessage('user', `执行操作: ${action.label}`);

  // 触发快捷操作事件
  emit('quick-action', action);

  // 如果有对应的菜单路径，导航到该页面
  if (action.menuPath) {
    emit('navigate-to', action.menuPath);
  }
}

function handleDragOver(e: DragEvent) {
  e.preventDefault();
  dragOver.value = true;
}

function handleDragLeave(e: DragEvent) {
  e.preventDefault();
  dragOver.value = false;
}

function handleDrop(e: DragEvent) {
  e.preventDefault();
  dragOver.value = false;

  const files = [...(e.dataTransfer?.files || [])];
  if (files.length > 0) {
    selectedFiles.value.push(...files);
  }
}

function handleFileSelect(e: Event) {
  const target = e.target as HTMLInputElement;
  const files = [...(target.files || [])];
  if (files.length > 0) {
    selectedFiles.value.push(...files);
  }
  // 清空input值，允许重复选择同一文件
  target.value = '';
}

function removeFile(index: number) {
  selectedFiles.value.splice(index, 1);
}

function selectFiles() {
  fileInputRef.value?.click();
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  });
}

// 监听外部消息更新
function addAIResponse(content: string) {
  addMessage('ai', content);
}

// 暴露方法给父组件
defineExpose({
  addAIResponse,
  addMessage,
});

// 监听可见性变化，自动滚动到底部
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      nextTick(() => {
        scrollToBottom();
      });
    }
  },
);
</script>

<template>
  <div
    :style="panelStyle"
    class="bg-background border-border fixed left-0 top-0 z-50 flex h-full flex-col border-r shadow-lg transition-transform duration-300 ease-in-out"
  >
    <!-- 头部 -->
    <div
      class="border-border flex min-h-10 items-center justify-between border-b bg-pink-500 p-4"
    >
      <!-- <div class="flex items-center gap-2">
        <Bot class="size-5 text-primary" />
        <h3 class="font-semibold text-foreground">AI 助手</h3>
      </div>
      <VbenButton
        variant="ghost"
        size="sm"
        @click="emit('update:visible', false)"
      >
        ✕
      </VbenButton> -->
    </div>

    <!-- 聊天区域 -->
    <div ref="chatContainerRef" class="flex-1 overflow-hidden">
      <VbenScrollbar class="h-full p-4">
        <div class="space-y-4">
          <div
            v-for="message in messages"
            :key="message.id"
            class="flex gap-3"
            :class="{
              'justify-end': message.type === 'user',
              'justify-start':
                message.type === 'ai' || message.type === 'system',
            }"
          >
            <!-- 头像 -->
            <div
              v-if="message.type !== 'user'"
              class="bg-primary/10 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full"
            >
              <Bot v-if="message.type === 'ai'" class="text-primary size-4" />
              <div v-else class="bg-muted-foreground size-2 rounded-full"></div>
            </div>

            <!-- 消息内容 -->
            <div
              class="max-w-[80%] rounded-lg px-3 py-2 text-sm"
              :class="{
                'bg-primary text-primary-foreground': message.type === 'user',
                'bg-muted text-foreground': message.type === 'ai',
                'bg-muted/50 text-muted-foreground text-xs':
                  message.type === 'system',
              }"
            >
              <div>{{ message.content }}</div>

              <!-- 文件列表 -->
              <div
                v-if="message.files && message.files.length > 0"
                class="mt-2 space-y-1"
              >
                <div
                  v-for="file in message.files"
                  :key="file.name"
                  class="rounded bg-black/10 px-2 py-1 text-xs opacity-80"
                >
                  📎 {{ file.name }} ({{ formatFileSize(file.size) }})
                </div>
              </div>

              <div class="mt-1 text-xs opacity-60">
                {{ formatTime(message.timestamp) }}
              </div>
            </div>

            <!-- 用户头像 -->
            <div
              v-if="message.type === 'user'"
              class="bg-primary flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full"
            >
              <User class="text-primary-foreground size-4" />
            </div>
          </div>

          <!-- 加载指示器 -->
          <div v-if="isLoading" class="flex justify-start gap-3">
            <div
              class="bg-primary/10 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full"
            >
              <Loader2 class="text-primary size-4 animate-spin" />
            </div>
            <div class="bg-muted text-foreground rounded-lg px-3 py-2 text-sm">
              AI正在思考中...
            </div>
          </div>
        </div>
      </VbenScrollbar>
    </div>

    <!-- 快捷操作按钮 -->
    <div class="border-border border-t px-4 py-2">
      <div class="grid grid-cols-5 gap-1.5">
        <VbenButton
          v-for="action in quickActions.slice(0, 10)"
          :key="action.id"
          variant="outline"
          size="sm"
          class="h-6 min-w-0 flex-shrink-0 justify-center rounded-full px-2 text-xs"
          @click="handleQuickAction(action)"
        >
          {{ action.label }}
        </VbenButton>
      </div>
    </div>

    <!-- 文件预览区域 -->
    <div v-if="hasFiles" class="px-4 pb-2">
      <div class="text-muted-foreground mb-2 text-xs">已选择文件</div>
      <div class="max-h-20 space-y-1 overflow-y-auto">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="bg-muted flex items-center justify-between rounded px-2 py-1 text-xs"
        >
          <span class="truncate">📎 {{ file.name }}</span>
          <VbenButton
            variant="ghost"
            size="sm"
            class="hover:bg-destructive hover:text-destructive-foreground h-4 w-4 p-0"
            @click="removeFile(index)"
          >
            ✕
          </VbenButton>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="border-border border-t p-4">
      <div
        class="relative"
        :class="{
          'ring-primary ring-2 ring-offset-2': dragOver,
        }"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
      >
        <div class="flex gap-2">
          <VbenButton
            variant="outline"
            size="sm"
            class="flex-shrink-0"
            @click="selectFiles"
          >
            <Paperclip class="size-4" />
          </VbenButton>

          <VbenInput
            v-model:value="inputValue"
            placeholder="输入消息或拖拽文件到此处..."
            class="flex-1"
            @keydown.enter.prevent="sendMessage"
          />

          <VbenButton
            size="sm"
            :disabled="!inputValue.trim() && !hasFiles"
            @click="sendMessage"
          >
            <Send class="size-4" />
          </VbenButton>
        </div>

        <!-- 拖拽提示 -->
        <div
          v-if="dragOver"
          class="bg-primary/10 border-primary text-primary absolute inset-0 flex items-center justify-center rounded-md border-2 border-dashed font-medium"
        >
          拖拽文件到此处
        </div>
      </div>

      <!-- 隐藏的文件输入 -->
      <input
        ref="fileInputRef"
        type="file"
        multiple
        class="hidden"
        @change="handleFileSelect"
      />
    </div>
  </div>
</template>

<style scoped>
.scrollbar-container {
  scrollbar-color: hsl(var(--muted-foreground)) transparent;
  scrollbar-width: thin;
}

.scrollbar-container::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-container::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-container::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.scrollbar-container::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--foreground));
}
</style>

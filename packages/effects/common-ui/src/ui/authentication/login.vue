<script setup lang="ts">
import type { VbenFormSchema } from '@vben-core/form-ui';

import type { AuthenticationProps, LoginAndRegisterParams } from './types';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { $t } from '@vben/locales';

import { useVbenForm } from '@vben-core/form-ui';
import { VbenButton, VbenCheckbox } from '@vben-core/shadcn-ui';
import { cloneDeep } from '@vben-core/shared/utils';

import Title from './auth-title.vue';
import ThirdPartyLogin from './third-party-login.vue';

interface Props extends AuthenticationProps {
  formSchema: VbenFormSchema[];
}

defineOptions({
  name: 'AuthenticationLogin',
});

const props = withDefaults(defineProps<Props>(), {
  codeLoginPath: '/auth/code-login',
  forgetPasswordPath: '/auth/forget-password',
  formSchema: () => [],
  loading: false,
  qrCodeLoginPath: '/auth/qrcode-login',
  registerPath: '/auth/register',
  showCodeLogin: true,
  showForgetPassword: true,
  showQrcodeLogin: true,
  showRegister: true,
  showRememberMe: true,
  showThirdPartyLogin: true,
  submitButtonText: '',
  subTitle: '',
  title: '',
});

const emit = defineEmits<{
  submit: [LoginAndRegisterParams];
}>();

const [Form, formApi] = useVbenForm(
  reactive({
    commonConfig: {
      hideLabel: true,
      hideRequiredMark: true,
    },
    schema: computed(() => props.formSchema),
    showDefaultActions: false,
  }),
);
const router = useRouter();

const REMEMBER_ME_KEY = `REMEMBER_ME_USERNAME_${location.hostname}`;
const REMEMBER_PASSWORD_KEY = `REMEMBER_ME_PASSWORD_${location.hostname}`;

const localUsername = localStorage.getItem(REMEMBER_ME_KEY) || '';
const localPassword = localStorage.getItem(REMEMBER_PASSWORD_KEY) || '';

const rememberMe = ref(!!localUsername);

// 简单的编码/解码函数（注意：这不是真正的加密，只是简单的混淆）
function encodePassword(password: string): string {
  return btoa(unescape(encodeURIComponent(password)));
}

function decodePassword(encodedPassword: string): string {
  try {
    return decodeURIComponent(escape(atob(encodedPassword)));
  } catch {
    return '';
  }
}

async function handleSubmit() {
  const { valid } = await formApi.validate();
  if (valid) {
    const values = cloneDeep(await formApi.getValues());
    if (rememberMe.value) {
      localStorage.setItem(REMEMBER_ME_KEY, values?.username || '');
      localStorage.setItem(
        REMEMBER_PASSWORD_KEY,
        encodePassword(values?.password || ''),
      );
    } else {
      localStorage.setItem(REMEMBER_ME_KEY, '');
      localStorage.setItem(REMEMBER_PASSWORD_KEY, '');
    }
    // 加上认证类型
    (values as any).grantType = 'password';
    emit('submit', values as LoginAndRegisterParams);
  }
}

function handleGo(path: string) {
  router.push(path);
}

onMounted(() => {
  if (localUsername) {
    formApi.setFieldValue('username', localUsername);
  }
  if (localPassword) {
    const decodedPassword = decodePassword(localPassword);
    if (decodedPassword) {
      formApi.setFieldValue('password', decodedPassword);
    }
  }
});

defineExpose({
  getFormApi: () => formApi,
});
</script>

<template>
  <div @keydown.enter.prevent="handleSubmit">
    <slot name="title">
      <Title>
        <slot name="title">
          {{ title || `${$t('authentication.welcomeBack')} 👋🏻` }}
        </slot>
        <template #desc>
          <span class="text-muted-foreground">
            <slot name="subTitle">
              {{ subTitle || $t('authentication.loginSubtitle') }}
            </slot>
          </span>
        </template>
      </Title>
    </slot>

    <Form />

    <div
      v-if="showRememberMe || showForgetPassword"
      class="mb-6 flex justify-between"
    >
      <div class="flex-center flex-col items-start">
        <VbenCheckbox
          v-if="showRememberMe"
          v-model:checked="rememberMe"
          name="rememberMe"
        >
          {{ $t('authentication.rememberMe') }}
        </VbenCheckbox>
      </div>

      <span
        v-if="showForgetPassword"
        class="vben-link text-sm font-normal"
        @click="handleGo(forgetPasswordPath)"
      >
        {{ $t('authentication.forgetPassword') }}
      </span>
    </div>
    <VbenButton
      :class="{
        'cursor-wait': loading,
      }"
      :loading="loading"
      aria-label="login"
      class="w-full"
      @click="handleSubmit"
    >
      {{ submitButtonText || $t('common.login') }}
    </VbenButton>

    <div
      v-if="showCodeLogin || showQrcodeLogin"
      class="mb-2 mt-4 flex items-center justify-between"
    >
      <VbenButton
        v-if="showCodeLogin"
        class="w-1/2"
        variant="outline"
        @click="handleGo(codeLoginPath)"
      >
        {{ $t('authentication.mobileLogin') }}
      </VbenButton>
      <VbenButton
        v-if="showQrcodeLogin"
        class="ml-4 w-1/2"
        variant="outline"
        @click="handleGo(qrCodeLoginPath)"
      >
        {{ $t('authentication.qrcodeLogin') }}
      </VbenButton>
    </div>

    <!-- 第三方登录 -->
    <slot v-if="showThirdPartyLogin" name="third-party-login">
      <ThirdPartyLogin />
    </slot>

    <slot name="to-register">
      <div v-if="showRegister" class="mt-3 text-center text-sm">
        {{ $t('authentication.accountTip') }}
        <span
          class="vben-link text-sm font-normal"
          @click="handleGo(registerPath)"
        >
          {{ $t('authentication.createAccount') }}
        </span>
      </div>
    </slot>
  </div>
</template>

<script lang="ts" setup>
import { h } from 'vue';

import { alert, prompt, VbenButton } from '@vben/common-ui';

import { Input, RadioGroup } from 'ant-design-vue';
import { BadgeJapaneseYen } from 'lucide-vue-next';

function showPrompt() {
  prompt({
    content: '请输入一些东西',
  })
    .then((val) => {
      alert(`已收到你的输入：${val}`);
    })
    .catch(() => {
      alert('Canceled');
    });
}

function showSelectPrompt() {
  prompt({
    component: Input,
    componentProps: {
      placeholder: '请输入',
      prefix: '充值金额',
      type: 'number',
    },
    componentSlots: {
      addonAfter: () => h(BadgeJapaneseYen),
    },
    content: '此弹窗演示了如何使用componentSlots传递自定义插槽',
    icon: 'question',
    modelPropName: 'value',
  }).then((val) => {
    if (val) alert(`你输入的是${val}`);
  });
}

function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function showAsyncPrompt() {
  prompt({
    async beforeClose(scope) {
      console.log(scope);
      if (scope.isConfirm) {
        if (scope.value) {
          // 模拟异步操作，如果不成功，可以返回false
          await sleep(2000);
        } else {
          alert('请选择一个选项');
          return false;
        }
      }
    },
    component: RadioGroup,
    componentProps: {
      class: 'flex flex-col',
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
        { label: 'Option 3', value: 'option3' },
      ],
    },
    content: '选择一个选项后再点击[确认]',
    icon: 'question',
    modelPropName: 'value',
  }).then((val) => {
    alert(`${val} 已设置。`);
  });
}
</script>
<template>
  <div class="flex gap-4">
    <VbenButton @click="showPrompt">Prompt</VbenButton>
    <VbenButton @click="showSelectPrompt">Prompt With Select</VbenButton>
    <VbenButton @click="showAsyncPrompt">Prompt With Async</VbenButton>
  </div>
</template>

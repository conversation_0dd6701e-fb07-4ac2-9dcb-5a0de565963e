# 项目更新

## 为什么无法像 npm 插件一样更新

因为项目是一个完整的项目模版，不是一个插件或者安装包，无法像插件一样更新，你使用代码后，会根据业务需求，进行二次开发，需要自行手动合并升级。

## 我需要怎么做

项目采用了 `Monorepo` 的方式进行管理，并将一些比较核心的代码进行了抽离，比如 `packages/@core`、`packages/effects`，只要业务代码没有修改这部分代码，那么你可以直接拉取最新代码，然后合并到你的分支上，只需要简单的处理部分冲突即可。其余文件夹只会进行一些小的调整，不会对业务代码产生影响。

::: tip 推荐

建议关注仓库动态，积极去合并，不要长时间积累，否则将会导致合并冲突过多，增加合并难度。

:::

## 使用 Git 更新代码

1. 克隆代码

```bash
git clone https://github.com/vbenjs/vue-vben-admin.git
```

2. 添加自己的公司 git 源地址

```bash
# up 为源名称,可以随意设置
# gitUrl为开源最新代码
git remote add up gitUrl;
```

3. 提交代码到自己公司 git

```bash
# 提交代码到自己公司
# main为分支名 需要自行根据情况修改
git push up main

# 同步公司的代码
# main为分支名 需要自行根据情况修改
git pull up main
```

4. 如何同步开源最新代码

```bash
git pull origin main
```

::: tip 提示

同步代码的时候会出现冲突。只需要把冲突解决即可

:::
